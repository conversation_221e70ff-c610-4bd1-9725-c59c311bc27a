"use client";
import { useEffect, useState } from "react";
import { <PERSON><PERSON>icker, DateRange, CaptionProps } from "react-day-picker";
import { ChevronLeft, ChevronRight } from "lucide-react";

import { cn } from "../../lib/utils";

export type SingleModeProps = {
  mode: "single";
  value: Date | undefined;
  onChange: (value: Date | undefined) => void;
};

export type RangeModeProps = {
  mode: "range";
  value: DateRange | undefined;
  onChange: (value: DateRange | undefined) => void;
};

export type View = "day" | "month" | "year";

export type CalendarProps = (SingleModeProps | RangeModeProps) & {
  minDate?: Date;
  maxDate?: Date;
  className?: string;
  classNames?: Record<string, string>;
  disabled?: (date: Date) => boolean;
  showOutsideDays?: boolean;
  view?: View;
  onViewChange?: (view: View) => void;
};

function Calendar({
  mode,
  value,
  onChange,
  minDate,
  maxDate,
  className,
  classNames,
  disabled,
  view: controlledView,
  onViewChange,
}: CalendarProps) {
  const [internalView, setInternalView] = useState<View>("day");
  const [currentYear, setCurrentYear] = useState(() => {
    const currentDate = new Date();

    if (!value) return currentDate.getFullYear();

    if ("from" in value) {
      return value.from?.getFullYear() ?? currentDate.getFullYear();
    } else {
      return value.getFullYear();
    }
  });
  const [currentMonth, setCurrentMonth] = useState(() => {
    const currentDate = new Date();

    if (!value) return currentDate.getMonth();

    if ("from" in value) {
      return value.from?.getMonth() ?? currentDate.getMonth();
    } else {
      return value.getMonth();
    }
  });

  const view = controlledView ?? internalView;

  const handleViewChange = (newView: View) => {
    if (onViewChange) {
      onViewChange(newView);
    } else {
      setInternalView(newView);
    }
  };

  const handleYearSelect = (year: number) => {
    setCurrentYear(year);
    handleViewChange("month");
  };

  const handleMonthSelect = (month: number) => {
    setCurrentMonth(month);
    handleViewChange("day");
  };

  const handleDecadeChange = (direction: "previous" | "next") => {
    setCurrentYear((prev) => prev + (direction === "next" ? 10 : -10));
  };

  const handleYearChange = (direction: "previous" | "next") => {
    setCurrentYear((prev) => prev + (direction === "next" ? 1 : -1));
  };

  const handleMonthChange = (direction: "previous" | "next") => {
    setCurrentMonth((prevMonth) => {
      const isNext = direction === "next";
      const newMonth = isNext ? prevMonth + 1 : prevMonth - 1;

      if (newMonth < 0) {
        setCurrentYear((prevYear) => prevYear - 1);
        return 11;
      } else if (newMonth > 11) {
        setCurrentYear((prevYear) => prevYear + 1);
        return 0;
      }

      return newMonth;
    });
  };

  const CustomCaption = ({ displayMonth }: CaptionProps) => {
    const isFirstMonth =
      mode === "range" &&
      displayMonth.getMonth() === currentMonth &&
      displayMonth.getFullYear() === currentYear;

    const isSecondMonth =
      mode === "range" &&
      displayMonth.getMonth() ===
        (currentMonth + 1 > 11 ? 0 : currentMonth + 1) &&
      displayMonth.getFullYear() ===
        (currentMonth + 1 > 11 ? currentYear + 1 : currentYear);
    return (
      <div className="flex justify-between items-center h-10 mb-3">
        {mode === "single" || isFirstMonth ? (
          <NavButton
            direction="left"
            onClick={() => handleMonthChange("previous")}
          />
        ) : (
          <span className="w-6" />
        )}

        <div className="flex items-center gap-1">
          <button
            onClick={() => setInternalView("month")}
            className="text-sm font-semibold leading-10 text-[#1e1e1e] cursor-pointer"
          >
            {`${displayMonth.toLocaleString("default", {
              month: "long",
            })}`}
          </button>
          <button
            onClick={() => setInternalView("year")}
            className="text-sm font-semibold leading-10 text-[#1e1e1e] cursor-pointer"
          >
            {displayMonth.getFullYear()}
          </button>
        </div>

        {mode === "single" || isSecondMonth ? (
          <NavButton
            direction="right"
            onClick={() => handleMonthChange("next")}
          />
        ) : (
          <span className="w-6" />
        )}
      </div>
    );
  };

  const renderYearNavigation = () => {
    const startYear = currentYear - (currentYear % 10);
    const years = Array.from({ length: 20 }, (_, i) => startYear + i - 1);

    return (
      <div className="px-4 pt-5 pb-8 border rounded-3xl max-w-[335px]">
        <div className="flex justify-between items-center mb-3 h-10">
          <NavButton
            direction="left"
            onClick={() => handleDecadeChange("previous")}
          ></NavButton>

          <span className="text-sm font-semibold leading-10">
            {years[0]} - {years[years.length - 1]}
          </span>

          <NavButton
            direction="right"
            onClick={() => handleDecadeChange("next")}
          ></NavButton>
        </div>
        <div className="grid grid-cols-4 gap-2">
          {years.map((year) => (
            <button
              key={year}
              onClick={() => handleYearSelect(year)}
              className={cn(
                "p-2 text-sm rounded-full font-normal",
                year === currentYear
                  ? "bg-primary text-white"
                  : "text-[#797878] hover:bg-gray-200"
              )}
            >
              {year}
            </button>
          ))}
        </div>
      </div>
    );
  };

  const renderMonthNavigation = () => {
    const months = Array.from({ length: 12 }, (_, i) => i);

    return (
      <div className="px-4 py-5 border rounded-3xl max-w-[335px]">
        <div className="flex justify-between items-center mb-3 h-10">
          <NavButton
            direction="left"
            onClick={() => handleYearChange("previous")}
          ></NavButton>

          <span className="text-sm font-semibold leading-10">
            {currentYear}
          </span>

          <NavButton
            direction="right"
            onClick={() => handleYearChange("next")}
          ></NavButton>
        </div>
        <div className="grid grid-cols-3 gap-y-2 gap-x-1">
          {months.map((month) => (
            <button
              key={month}
              onClick={() => handleMonthSelect(month)}
              className={cn(
                "p-2 text-sm font-normal rounded-full",
                month === currentMonth
                  ? "bg-primary text-white "
                  : "text-[#797878] hover:bg-gray-200"
              )}
            >
              {new Date(0, month).toLocaleString("default", { month: "long" })}
            </button>
          ))}
        </div>
      </div>
    );
  };

  const customWeekdays = ["SUN", "MON", "TUE", "WED", "THU", "FRI", "SAT"];

  const commonDayPickerProps = {
    fromDate: minDate,
    toDate: maxDate,
    month: new Date(currentYear, currentMonth),
    disabled,
    showOutsideDays: false,
    numberOfMonths: mode === "range" ? 2 : 1,
    pagedNavigation: mode === "range",
    components: { Caption: CustomCaption },
    formatters: {
      formatWeekdayName: (day: Date) => customWeekdays[day.getDay()],
    },
    className: cn(
      "py-5 px-4 rounded-3xl border",
      mode === "range" ? "max-w-[700px] px-5" : "max-w-[335px]",
      className
    ),
    classNames: {
      months: "flex flex-row gap-4",
      table: "w-full border-collapse",
      head_row: cn(
        "grid grid-flow-col auto-cols-fr",
        mode === "range" && "grid -ml-1"
      ),
      head_cell: cn(
        "text-[#B0B0B0] font-normal text-xs p-2",
        mode === "range" && "text-[10px]"
      ),
      row: "grid grid-flow-col auto-cols-fr",
      cell: cn(
        "relative p-0 h-8 my-1 text-[#6a6a6d] inline-flex items-center justify-center focus-within:relative focus-within:z-20",
        mode === "range" &&
          "h-6 [&:has(>.day-range-end)]:rounded-r-full [&:has(>.day-range-end)]:mr-1.5 [&:has(>.day-range-end)]:justify-end [&:has(>.day-range-start)]:rounded-l-full [&:has(>.day-range-start)]:ml-1.5 [&:has(>.day-range-start)]:justify-start [&:has([aria-selected])]:bg-[#EBE0F4] [&:has([aria-selected].day-range-start.day-range-end)]:bg-transparent"
      ),
      day: cn(
        "size-8 p-0 rounded-full text-sm",
        mode === "range" && "text-xs size-6 p-1"
      ),
      day_range_start: "day-range-start",
      day_range_end: "day-range-end",
      day_selected: "bg-primary text-primary-foreground",
      day_outside: cn(
        "text-[#d1d1d1] opacity-0 pointer-events-none",
        "aria-selected:bg-transparent !bg-transparent"
      ),
      day_disabled: "text-[#d1d1d1]",
      day_range_middle:
        "day-range-middle aria-selected:bg-transparent aria-selected:text-[#797878]",
      day_hidden: "invisible",
      ...classNames,
    },
  };

  useEffect(() => {
    if (!value) return;

    if ("from" in value) {
      setCurrentMonth((prev) => value.from?.getMonth() ?? prev);
      setCurrentYear((prev) => value.from?.getFullYear() ?? prev);
    } else {
      setCurrentMonth(value.getMonth());
      setCurrentYear(value.getFullYear());
    }
  }, [value]);

  return (
    <div className="relative">
      {view === "month" && renderMonthNavigation()}
      {view === "year" && renderYearNavigation()}
      {view === "day" &&
        (mode === "single" ? (
          <DayPicker
            {...commonDayPickerProps}
            mode="single"
            selected={value as Date}
            onSelect={onChange as (date: Date | undefined) => void}
          />
        ) : (
          <DayPicker
            {...commonDayPickerProps}
            mode="range"
            selected={value as DateRange}
            onSelect={onChange as (range: DateRange | undefined) => void}
          />
        ))}
    </div>
  );
}

interface NavButtonProps {
  direction: "left" | "right";
  onClick?: () => void;
}

function NavButton({ direction, onClick }: NavButtonProps) {
  return (
    <button
      className={cn(
        "bg-transparent text-[#1e1e1e] size-6 border-none shadow-none rounded hover:bg-gray-200 inline-flex justify-center items-center"
      )}
      onClick={onClick}
    >
      {direction === "left" && <ChevronLeft className="size-4"></ChevronLeft>}
      {direction === "right" && (
        <ChevronRight className="size-4"></ChevronRight>
      )}
    </button>
  );
}

export { Calendar };

Calendar.displayName = "Calendar";
