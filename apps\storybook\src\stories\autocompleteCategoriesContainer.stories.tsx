import React, { useState } from "react";
import type { Meta, StoryObj } from "@storybook/react";
import { AutocompleteCategoriesContainer, debitCategories } from "@repo/ui";

const meta: Meta<typeof AutocompleteCategoriesContainer> = {
  title: "Filters/AutocompleteCategoriesContainer",
  component: AutocompleteCategoriesContainer,
  tags: ["autodocs"],
};

export default meta;
type Story = StoryObj<typeof AutocompleteCategoriesContainer>;

export const Interactive: Story = {
  render: () => {
    const [selectedTags, setSelectedTags] = useState<string[]>([]);

    return (
      <div className="w-80 bg-white border rounded-md shadow-lg p-4 flex flex-col space-y-3">
        <div className="flex flex-col space-y-4">
                <div className="text-gray-700 font-semibold text-sm">
                  Category
                </div>
              </div>
       
        <AutocompleteCategoriesContainer
          categories={debitCategories}
          selectedTags={selectedTags}
          onTagsChange={(tags) => {
            setSelectedTags(tags);
            console.log("Selected Tags:", tags);
          }}
        />
      </div>
    );
  },
};
