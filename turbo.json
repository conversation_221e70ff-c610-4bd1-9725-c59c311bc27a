{"$schema": "https://turbo.build/schema.json", "tasks": {"build": {"dependsOn": ["^build"], "inputs": ["$TURBO_DEFAULT$", "!**/*.md", "!.gitlab-ci.yml", ".env.production.local", ".env.local", ".env.production", ".env"], "outputs": ["dist/**", ".next/**", "!.next/cache/**", "storybook-static/**"], "env": ["MONGODB_URI", "MONGODB_DB_NAME", "GRPC_SERVER_URI", "PROTO_DIR_PATH", "SHOW_DUMMY_DATA"]}, "type-check": {}, "dev": {"cache": false, "persistent": true}, "clean": {"cache": false}, "chromatic": {"cache": false, "env": ["CHROMATIC_PROJECT_TOKEN"]}, "test:e2e": {"dependsOn": ["^build"], "passThroughEnv": ["BASE_URL", "DEBUG", "UPDATE_SNAPSHOTS", "PLAYWRIGHT_*", "CI"]}}}