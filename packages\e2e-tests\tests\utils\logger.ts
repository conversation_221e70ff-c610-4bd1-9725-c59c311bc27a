/**
 * Log levels
 */
export enum LogLevel {
  DEBUG = 0,
  INFO = 1,
  WARN = 2,
  ERROR = 3,
  NONE = 4
}

/**
 * Logger utility for test automation
 */
export class Logger {
  private prefix: string;
  private static logLevel: LogLevel = LogLevel.INFO;

  /**
   * Create a new logger instance
   * @param prefix - Prefix to add to all log messages
   */
  constructor(prefix: string = '') {
    this.prefix = prefix;
  }

  /**
   * Set the global log level
   * @param level - Log level
   */
  static setLogLevel(level: LogLevel): void {
    Logger.logLevel = level;
  }

  /**
   * Get the current log level
   */
  static getLogLevel(): LogLevel {
    return Logger.logLevel;
  }

  /**
   * Log a debug message
   * @param message - Message to log
   */
  debug(message: string): void {
    if (Logger.logLevel <= LogLevel.DEBUG) {
      console.debug(`${this.getTimestamp()} [DEBUG] ${this.prefix} ${message}`);
    }
  }

  /**
   * Log an info message
   * @param message - Message to log
   */
  info(message: string): void {
    if (Logger.logLevel <= LogLevel.INFO) {
      console.info(`${this.getTimestamp()} [INFO] ${this.prefix} ${message}`);
    }
  }

  /**
   * Log a warning message
   * @param message - Message to log
   */
  warn(message: string): void {
    if (Logger.logLevel <= LogLevel.WARN) {
      console.warn(`${this.getTimestamp()} [WARN] ${this.prefix} ${message}`);
    }
  }

  /**
   * Log an error message
   * @param message - Message to log
   * @param error - Optional error object
   */
  error(message: string, error?: Error): void {
    if (Logger.logLevel <= LogLevel.ERROR) {
      console.error(`${this.getTimestamp()} [ERROR] ${this.prefix} ${message}`);
      if (error) {
        console.error(error);
      }
    }
  }

  /**
   * Get current timestamp
   */
  private getTimestamp(): string {
    const now = new Date();
    return now.toISOString();
  }
}
