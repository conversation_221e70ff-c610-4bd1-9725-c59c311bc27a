import { NextResponse } from "next/server";
import { getAccessibleGroups, getAccessibleUsers } from "../../lib/user";
import { mapApiResponseToGroupsServerWithAccounts, mapApiResponseToMembersServerWithAccounts } from "../../lib/member-mapper";


export async function GET(request: Request) {
  const { searchParams } = new URL(request.url);
  const authToken = searchParams.get('authToken') || '';
  
  if (!authToken) {
    return NextResponse.json(
      { error: 'Authentication token is required' }, 
      { status: 401 }
    );
  }
  
  try {    
    const [usersResponse, groupsResponse] = await Promise.all([
      getAccessibleUsers(authToken),
      getAccessibleGroups(authToken)
    ]);
        const [members, groups] = await Promise.all([
      mapApiResponseToMembersServerWithAccounts(usersResponse, authToken),
      mapApiResponseToGroupsServerWithAccounts(groupsResponse, authToken)
    ]);    
    return NextResponse.json({ 
      members, 
      groups,
      _debug: {
        memberCount: members.length,
        groupCount: groups.length,
        userCount: usersResponse.users.length
      }
    });
  } catch (error) {
    return NextResponse.json(
      { error: 'Failed to fetch members and groups', details: String(error) }, 
      { status: 500 }
    );
  }
}