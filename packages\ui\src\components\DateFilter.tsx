"use client";
import { useEffect, useState } from "react";
import { DateRange } from "react-day-picker";
import { format, startOfWeek, startOfMonth, startOfYear } from "date-fns";
import { Calendar } from "../components/ui/calendar";
import { Label } from "../components/ui/label";
import { CalendarIcon } from "../icons";
import { cn } from "../lib/utils";
import { Separator } from "./ui/separator";

interface DateFilterProps {
  initialRange?: DateRange;
  initialPreset?: string;
  onFilterChange?: (filters: {
    startDate: string;
    endDate: string;
    range?: DateRange;
  }) => void;
  onCalendarChange?: (range: DateRange | undefined) => void;
}

const DateFilter = ({
  initialRange,
  initialPreset = "",
  onFilterChange,
  onCalendarChange,
}: DateFilterProps) => {
  const [selectedRange, setSelectedRange] = useState<DateRange | undefined>(
    initialRange || undefined
  );
  const [selectedPreset, setSelectedPreset] = useState<string>(initialPreset);
  const [calendarOpen, setCalendarOpen] = useState<boolean>(false);

  const onCalendarOpenChange = (open: boolean) => {
    setCalendarOpen(open);
  };

  const handlePresetSelection = (preset: string) => {
    if (selectedPreset === preset) {
      setSelectedRange(undefined);
      setSelectedPreset("");
      onFilterChange?.({ startDate: "", endDate: "", range: undefined });
      onCalendarChange?.(undefined);
      return;
    }

    let range: DateRange | undefined;
    const today = new Date();

    switch (preset) {
      case "Today":
        range = { from: today, to: today };
        break;

      case "This week":
        range = { from: startOfWeek(today), to: today };
        break;
      case "This month":
        range = { from: startOfMonth(today), to: today };
        break;
      case "This Year":
        range = { from: startOfYear(today), to: today };
        break;
      default:
        range = undefined;
    }

    setSelectedRange(range);
    setSelectedPreset(preset);

    if (range?.from && range?.to) {
      onFilterChange?.({
        startDate: format(range.from, "yyyy-MM-dd"),
        endDate: format(range.to, "yyyy-MM-dd"),
        range,
      });
      onCalendarChange?.(range);
    }
  };

  const formatSafeDate = (
    date: Date | undefined,
    formatString: string
  ): string => {
    if (!date || !(date instanceof Date) || isNaN(date.getTime())) {
      if (!selectedRange?.from && !selectedRange?.to) {
        return "Select date";
      }
      if (formatString.includes("MM-dd") && date === undefined) {
        if (
          selectedRange?.from &&
          !selectedRange?.to &&
          date === selectedRange?.to
        ) {
          return "Select date";
        }
        if (
          !selectedRange?.from &&
          selectedRange?.to &&
          date === selectedRange?.from
        ) {
          return "Select date";
        }
      }

      return "Select date";
    }
    try {
      return format(date, formatString);
    } catch (error) {
      console.error("Error formatting date:", error);
      return "Invalid date";
    }
  };

  const handleCalendarChange = (value: DateRange | undefined) => {
    if (value) {
      setSelectedRange(value);
      setSelectedPreset("");
      if (value.from && value.to) {
        onFilterChange?.({
          startDate: format(value.from, "yyyy-MM-dd"),
          endDate: format(value.to, "yyyy-MM-dd"),
          range: value,
        });
        onCalendarChange?.(value);
      }
    }
  };
  useEffect(() => {
    setSelectedRange(initialRange);
    if (!initialRange) {
      setSelectedPreset("");
    }
  }, [initialRange]);
  return (
    <div className="w-full flex flex-col">
      <p className="  text-sm leading-10 ">Date</p>
      <div className="flex gap-[14.5px] mb-7 mt-1">
        {["Today", "This week", "This month", "This Year"].map((preset) => (
          <button
            key={preset}
            className={cn(
              "cursor-pointer flex whitespace-nowrap items-center justify-center p-2 text-sm py-1.5 px-2.5 rounded-2xl border border-[#F6F6F6]",
              selectedPreset === preset
                ? "bg-foreground text-white border border-foreground"
                : "bg-white text-[#797878]"
            )}
            onClick={() => handlePresetSelection(preset)}
          >
            {preset}
          </button>
        ))}
      </div>

      <div className="flex gap-2 items-center mb-7">
        <div className="flex flex-col gap-2.5 w-full" key="startDate">
          <Label className="text-sm ">Start date</Label>
          <div
            className="bg-white text-xs rounded-lg flex cursor-pointer justify-between py-3 px-4 gap-8 border border-[#DAD9DC] whitespace-nowrap"
            onClick={() => onCalendarOpenChange(!calendarOpen)}
          >
            {formatSafeDate(selectedRange?.from, "MM-dd-yyyy")}
            <CalendarIcon className="text-primary" />
          </div>
        </div>
        <div className="w-3 border-t border-[#C4C4C4] mt-6" />
        <div className="flex flex-col gap-2.5 w-full" key="endDate">
          <Label className="text-sm ">End date</Label>
          <div
            className="bg-white text-xs rounded-lg cursor-pointer flex justify-between py-3 px-4 gap-8 border border-[#DAD9DC] whitespace-nowrap"
            onClick={() => onCalendarOpenChange(!calendarOpen)}
          >
            {formatSafeDate(selectedRange?.to, "MM-dd-yyyy")}
            <CalendarIcon height={18} width={18} className="text-primary" />
          </div>
        </div>
      </div>
      {calendarOpen && (
        <div className="flex flex-col gap-4 mb-4 items-center">
          <Separator />
          <Calendar
            mode="range"
            value={selectedRange}
            onChange={handleCalendarChange}
            className="rounded-md"
            maxDate={new Date()}
          />
        </div>
      )}
    </div>
  );
};

export { DateFilter };
