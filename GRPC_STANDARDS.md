
```

## gRPC Integration Standards

### 1. gRPC Client Creation and Management

Always use the centralized utility function to create gRPC clients:

```typescript
// Correct way to create a gRPC client
const transactionClient: DepositTransactionClient = createGrpcClient<DepositTransactionClient>({
  protoPath: GRPC_CONFIG.PROTO_FILES.TRANSACTION,
  servicePath: 'backend_services.visualization',
  serviceConstructor: 'DepositTransaction'
});
```

### 2. gRPC Request/Response Transformation

Always transform between snake_case (gRPC) and camelCase (Frontend) at the service boundary:

```typescript
// Types definition (camelCase)
interface TransactionFilters {
  userGroups?: {
    userIds: { id: string }[];
    userGroupIds: { id: string }[];
  };
  timeRange?: { 
    fromTime: number; 
    toTime: number 
  };
}

// gRPC request transformation (to snake_case)
const request: TransactionRequest = {
  filter: {
    user_groups: {
      user_ids: filters.userGroups?.userIds,
      user_group_ids: filters.userGroups?.userGroupIds
    },
    time_range: {
      from_time: String(filters.timeRange?.fromTime),
      to_time: String(filters.timeRange?.toTime)
    }
  }
};
```

### 3. gRPC Service Organization

Follow these patterns for organizing gRPC-related code:

1. **Types**: Keep all service-related types in a dedicated types file
   ```typescript
   // types/transaction.types.ts
   export interface TransactionFilters {
     // camelCase interface properties
   }
   
   export interface TransactionRequest {
     // snake_case properties matching proto
   }
   ```

2. **Service Files**: Organize service files by domain
   ```typescript
   // lib/transaction.ts
   import { TransactionFilters, TransactionRequest } from './types/transaction.types';
   import { createGrpcClient } from './grpc-utils';
   ```

3. **Utils**: Maintain utility functions for common transformations
   ```typescript
   // lib/utils/transaction-filters.ts
   export function applyTransactionFilters(filters: TransactionFilters): TransactionRequest {
     // Handle transformation here
   }
   ```

### 4. Best Practices for gRPC Integration

1. **Client Creation**
   - Use the centralized `createGrpcClient` utility
   - Cache client instances for reuse
   - Type clients properly with TypeScript interfaces

2. **Data Transformation**
   - Transform at service boundaries only
   - Use dedicated filter utility functions
   - Keep transformation logic isolated and testable

3. **Error Handling**
   - Handle gRPC specific errors appropriately
   - Transform error messages to user-friendly format
   - Maintain consistent error structure

4. **Type Safety**
   ```typescript
   // Define strict types for requests and responses
   type DepositTransactionClient = grpc.Client & {
     FetchDepositTxns: (
       request: TransactionRequest,
       metadata: grpc.Metadata,
       callback: (error: grpc.ServiceError | null, response: GetTransactionResponse) => void,
     ) => void;
   };
   ```

### 5. gRPC Function Pattern

Follow this pattern for all gRPC service functions:

```typescript
export function getTransactions(
  authToken: string,
  pageSize: number,
  pageNumber: number,
  filters?: TransactionFilters
): Promise<GetTransactionResponse> {
  // 1. Set metadata
  requestMetadata.set("authorization", authToken);

  // 2. Transform filters to request format
  const request = applyTransactionFilters(filters || {});
  request.pagination_params = {
    page_size: pageSize,
    page_number: pageNumber,
  };

  // 3. Make gRPC call
  return new Promise((resolve, reject) => {
    transactionClient.FetchDepositTxns(
      request,
      requestMetadata,
      (error, response) => {
        if (error) {
          console.error("FetchDepositTxns error:", error);
          reject(error);
        } else {
          // 4. Transform response if needed
          resolve(response);
        }
      },
    );
  });
}
```

## Best Practices

### 1. Consistent Naming
- **Variables**: `const userName = "john";`
- **Functions**: `function getUserData() {}`
- **Components**: `const UserProfile = () => {}`
- **Interfaces**: `interface UserData {}`
- **Props**: `{ userData, onUserSelect }`

### 2. API Boundary Management
- Transform data immediately when receiving from external sources
- Convert back to snake_case only when sending data to external APIs
- Never mix naming conventions within the same component or function

### 3. TypeScript Integration
- Always define interfaces with camelCase properties
- Use strict typing to catch naming inconsistencies
- Leverage IntelliSense for better developer experience

### 4. Testing Considerations
- Test components with both camelCase and snake_case data during transition
- Ensure transformation functions work correctly
- Validate that no snake_case properties leak into the UI

## Migration Checklist

When working with existing code or adding new features:

- [ ] Check if API responses are being transformed to camelCase
- [ ] Ensure TypeScript interfaces use camelCase properties
- [ ] Verify component props and state use camelCase
- [ ] Update dummy data to use camelCase
- [ ] Test with both old and new data formats during transition
- [ ] Remove snake_case fallbacks after migration is complete

## Troubleshooting

### Common Issues and Solutions

1. **Property undefined errors**
   ```typescript
   // Problem: Accessing snake_case property on camelCase object
   console.log(user.first_name); // undefined

   // Solution: Use camelCase property
   console.log(user.firstName); // "John"
   ```

2. **Mixed naming in components**
   ```typescript
   // Problem: Inconsistent naming
   const Component = ({ user_data, accountInfo }) => { /* ... */ };

   // Solution: Consistent camelCase
   const Component = ({ userData, accountInfo }) => { /* ... */ };
   ```

3. **API integration issues**
   ```typescript
   // Problem: Not transforming API response
   const data = await fetch('/api/users').then(r => r.json());

   // Solution: Transform at boundary
   const data = await fetch('/api/users')
     .then(r => r.json())
     .then(toCamelCase);
   ```

## Conclusion

Following these camelCase naming convention standards ensures:
- Consistent, readable code across the entire frontend
- Better TypeScript support and IntelliSense
- Easier maintenance and debugging
- Improved developer experience
- Seamless integration with external APIs while maintaining internal consistency

For questions or clarifications about these standards, please refer to the implementation examples in the codebase or reach out to the development team.
