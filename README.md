# Cuspweb

Welcome to the **Cuspweb** project! We've used `pnpm` as the package manager and `Storybook` for component-driven development.

## 🚧 Prerequisites

Ensure you have the following tools installed before proceeding:

- [Node.js](https://nodejs.org/) (>= 16.13.0)
- We are using [Corepack](https://nodejs.org/api/corepack.html) to manage the package manager we are using in this project, which is [pnpm](https://pnpm.io/). So, before running any `pnpm` commands in this project, please setup Corepack as follows:

  ```bash
  corepack enable && corepack enable npm
  ```

  To learn more about how Corepack works, please check out this [article](https://www.totaltypescript.com/how-to-use-corepack).

## 🚀 Getting Started

### 1. Clone the Repository

```bash
git clone https://gitlab.com/InfoCusp/cuspweb.git
cd cuspweb
```

### 2. Install Dependencies

Use pnpm to install the required dependencies:

```bash
pnpm install
```

### 3. Start Development Server

To run Web app and Storybook for component development:

```bash
pnpm dev
```

Web app will start at http://localhost:3000.
Storybook will start at http://localhost:6006.

#### 3.1 Start Web app only

```bash
pnpm dev --filter web
```

#### 3.1 Start Storybook only

```bash
pnpm dev --filter storybook
```
