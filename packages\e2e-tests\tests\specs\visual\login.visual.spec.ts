import { test, expect } from '../../fixtures/test-fixtures';
import { expectScreenshotMatch } from '../../utils/visual-comparison';

// Skip visual tests for now until we create baseline screenshots
test.describe.skip('Login Page Visual Tests', () => {
  test('should match the login page visual baseline', async ({ loginPage, page }) => {
    // Wait for the page to be fully loaded
    await loginPage.waitForPageLoad();

    // Take a screenshot and compare with baseline
    await expectScreenshotMatch(
      page,
      'login-page',
      {
        threshold: 0.1, // 0.1% threshold for differences
        updateSnapshots: process.env.UPDATE_SNAPSHOTS === 'true'
      }
    );
  });

  test('should match the phone input field visual baseline', async ({ loginPage }) => {
    // Wait for the page to be fully loaded
    await loginPage.waitForPageLoad();

    // Take a screenshot of the phone input and compare with baseline
    await expectScreenshotMatch(
      loginPage.phoneInput,
      'login-phone-input',
      {
        threshold: 0.1,
        updateSnapshots: process.env.UPDATE_SNAPSHOTS === 'true'
      }
    );
  });

  test('should match the send code button visual baseline', async ({ loginPage }) => {
    // Wait for the page to be fully loaded
    await loginPage.waitForPageLoad();

    // Take a screenshot of the send code button and compare with baseline
    await expectScreenshotMatch(
      loginPage.sendCodeButton,
      'login-send-code-button',
      {
        threshold: 0.1,
        updateSnapshots: process.env.UPDATE_SNAPSHOTS === 'true'
      }
    );
  });
});
