import { format } from "date-fns";
import { Check, Edit, Paperclip, X, XCircle } from "../icons";
import { BankIconMapping } from "./MemberGroupFilter";
import { TransactionSectionProps } from "./UpcomingTransactions";
import { calculateTotal, formatAmountDisplay, formatTotalAmount } from "./RecurrentTransactionDetails";

export function PaymentHistory({
  transactions,
  editMode,
  onToggleEditMode,
  onDeleteTransaction
}: TransactionSectionProps) {
  if (transactions.length === 0) {
    return null;
  }

  return (
    <div className="p-4 rounded-2xl border border-[#F4EEF9] flex flex-col gap-3 pl-7 bg-white">
      {/* Header */}
      <div className="flex justify-between text-sm">
        <div className="flex items-center gap-2 text-primary font-semibold">
          <span className="text-base font-medium">Payment history</span>
        </div>
        <div className="flex items-center gap-2">
          {editMode && (
            <span className="text-primary text-xs font-semibold underline cursor-pointer">
              Add
            </span>
          )}
          <div onClick={onToggleEditMode} className="cursor-pointer">
            {editMode ? (
              <span className="text-primary text-xl">
                <X />
              </span>
            ) : (
              <Edit width={24} height={24} />
            )}
          </div>
        </div>
      </div>

      {/* Total */}
      <div className="text-xs">
        <span className="text-[#6B6B6B] mr-1">Total</span>
        <span className="text-[1E1E1E] font-medium mr-1">
          {formatTotalAmount(calculateTotal(transactions))}
        </span>
        <span> for {transactions.length} transactions</span>
      </div>

      {/* Edit Mode Info Message */}
      {editMode && (
        <div className="p-3 rounded-lg flex items-start gap-2 mb-1 bg-[#E9C5EB]">
          <span className="text-primary">
            <XCircle width={20} height={20} />
          </span>
          <span className="text-xs text-[#905BB5]">
            Unlink your transaction to update the payee
          </span>
        </div>
      )}

      {/* Transaction List */}
      {transactions.map((txn, index) => (
        <div key={index} className="relative">
          <div
            className={`bg-[#F9F9F9] rounded-lg p-3 flex flex-col gap-2 ${
              editMode ? "w-[85%]" : ""
            }`}
          >
            <div className="flex justify-between items-center">
              <div className="text-[#1E1E1E] font-medium">
                {format(
                  new Date(Number(txn.txnCard.txnTimestamp) * 1000),
                  "do MMM yyyy"
                )}
              </div>
              <div className="text-[#1E1E1E] font-bold">
                {formatAmountDisplay(txn.txnCard.amount)}
              </div>
            </div>
            <div className="flex justify-between items-center">
              <div className="flex items-center gap-2">
                <BankIconMapping
                  bankName={txn.txnCard.account?.branch!}
                  className="w-4 h-4"
                />
                <span className="text-[#1E1E1E]">
                  {txn.txnCard.account?.holderName || "Jenny"}
                </span>
              </div>
              <div className="flex items-center gap-2">
                {txn.txnCard.documentsCount > 0 && (
                  <div className="bg-primary text-white rounded-md flex gap-1 items-center justify-center text-xs w-fit py-[3px] px-[5px]">
                    <span className="text-xs font-medium">
                      {txn.txnCard.documentsCount}
                    </span>
                    <span>
                      <Paperclip />
                    </span>
                  </div>
                )}
              </div>
            </div>
          </div>
          {editMode && onDeleteTransaction && (
            <div className="mr-7.5 ml-3">
              <div
                className="absolute top-1/2 -right-0 -translate-y-1/2 text-red-500 cursor-pointer"
                onClick={() => onDeleteTransaction(txn.txnCard.txnId)}
              >
                <Check width={24} height={24} />
              </div>
            </div>
          )}
        </div>
      ))}
    </div>
  );
}