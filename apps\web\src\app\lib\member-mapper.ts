import {  formatApiAccount, Group, Member } from "@repo/ui";
import { getAccounts } from "./account";
import { getAccessibleGroups, getAccessibleUsers } from "./user";

export interface ApiAccount {
  account_id: string;
  linked_acc_ref: string;
  masked_acc_number: string;
  fip_id: string;
  holder_name: string;
  branch: string;
  fi_type: string;
  user_id: string;
  data_synced_at: string;
}

export interface GetAccountsResponse {
  accounts: ApiAccount[];
}

export interface ApiUser {
  _id: {
    id: string;
  };
  phone_number: string;
  pan?: string;
  name: string;
  address?: string;
  date_of_birth?: string;
  family_id?: string;
  group_id?: string | null;
  is_primary?: boolean;
  last_login_time?: string;
  created_at?: string;
  updated_at?: string;
  nickname?: string;
  email?: string;
  relation?: string;
  alternate_phone_numbers?: string[];
}

export interface ApiGroup {
  group: {
    _id: {
      id: string;
    };
    name: string;
    family_id?: string;
    created_at?: string;
    updated_at?: string;
    created_by?: string;
  };
  users: ApiUser[];
}

export interface GetAccessibleUsersResponse {
  users: ApiUser[];
}

export interface GetAccessibleGroupsResponse {
  groups: ApiGroup[];
}
export async function mapApiResponseToMembersServerWithAccounts(
  response: GetAccessibleUsersResponse,
  authToken: string
): Promise<Member[]> {
  try {
    
    const accountsResponse = await getAccounts(authToken);
    const allAccounts = accountsResponse.accounts || [];    
    const userAccountsMap: Record<string, ApiAccount[]> = {};
    allAccounts.forEach((account) => {
      if (!userAccountsMap[account.user_id]) {
        userAccountsMap[account.user_id] = [];
      }
      userAccountsMap[account.user_id]!.push(formatApiAccount(account));
    });
    
    const members = response.users
    .map((user) => {
      const userId = user._id.id;
      const userAccounts = userAccountsMap[userId] || [];
    
      if (userAccounts.length === 0) {
        return null;
      }
    
      return {
        id: userId,
        name: user.name?.trim()
          ? user.name
          : (userAccounts[0]?.holder_name || user.phone_number),
        accounts: userAccounts,
      };
    })
    
      .filter(Boolean) as Member[]; 
    
    return members;
  } catch (error) {
    console.error("Error mapping members with accounts:", error);
    return [];
  }
}


export async function mapApiResponseToGroupsServerWithAccounts(
  response: GetAccessibleGroupsResponse,
  authToken: string
): Promise<Group[]> {
  try {
    const accountsResponse = await getAccounts(authToken);
    const allAccounts = accountsResponse.accounts || [];    
    const userAccountsMap: Record<string, ApiAccount[]> = {};
    allAccounts.forEach((account) => {
      if (!userAccountsMap[account.user_id]) {
        userAccountsMap[account.user_id] = [];
      }
      userAccountsMap[account.user_id]!.push(formatApiAccount(account));
    });
    
    const groups = response.groups
      .map((apiGroup) => {
        const members = apiGroup.users
        .map((user) => {
          const userId = user._id.id;
          const userAccounts = userAccountsMap[userId] || [];
        
          if (userAccounts.length === 0) {
            return null;
          }
        
          return {
            id: userId,
            name: user.name?.trim()
              ? user.name
              : (userAccounts[0]?.holder_name || user.phone_number),
            accounts: userAccounts,
          };
        })
        
          .filter(Boolean) as Member[];

        if (members.length === 0) {
          return null; 
        }

        return {
          id: apiGroup.group._id.id,
          groupName: apiGroup.group.name,
          members: members,
        };
      })
      .filter(Boolean) as Group[];
    
    return groups;
  } catch (error) {
    console.error("Error mapping groups with accounts:", error);
    return [];
  }
}
export async function getMembersAndGroups(authToken: string) {
  try {
    const [usersResponse, groupsResponse] = await Promise.all([
      getAccessibleUsers(authToken),
      getAccessibleGroups(authToken),
    ]);

    const [members, groups] = await Promise.all([
      mapApiResponseToMembersServerWithAccounts(usersResponse, authToken),
      mapApiResponseToGroupsServerWithAccounts(groupsResponse, authToken),
    ]);

    return { members, groups };
  } catch (error) {
    console.error("Failed to get members and groups:", error);
    return { members: [], groups: [] };
  }
}
