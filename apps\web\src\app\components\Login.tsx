"use client";
import {
  Button,
  Input,
  InputOTP,
  InputOTPGroup,
  InputOTPSlot,
  REGEXP_ONLY_DIGITS_AND_CHARS,
} from "@repo/ui";
import { Phone } from "@repo/ui/icons";
import {
  ConfirmationR<PERSON>ult,
  RecaptchaVerifier,
  signInWithPhoneNumber,
} from "firebase/auth";
import React, { useRef, useState } from "react";
import { firebaseAuth } from "../lib/firebase/config";
import { redirect } from "next/navigation";
import { addPhoneNumber, createSession } from "../actions/auth-actions";
import { CONSTANTS, ROUTE } from "../lib/constants";

const InputOtpComp = ({
  passkey,
  setPasskey,
}: {
  passkey: string;
  setPasskey: (key: string) => void;
}) => {
  return (
    <div className="w-full ml-4">
      <InputOTP
        maxLength={CONSTANTS.INPUT_OTP_MAX_LENGTH}
        pattern={REGEXP_ONLY_DIGITS_AND_CHARS}
        value={passkey}
        onChange={(value) => setPasskey(value)}
      >
        {[...new Array(CONSTANTS.INPUT_OTP_MAX_LENGTH)].map((_, i) => (
          <InputOTPGroup key={i}>
            <InputOTPSlot index={i} />
          </InputOTPGroup>
        ))}
      </InputOTP>
    </div>
  );
};

export default function Login({ session }: { session: string | null }) {
  const [mobileNumber, setMobileNumber] = useState("");
  const [passkey, setPasskey] = useState("");
  const [isValidPhoneNumber, setIsValidPhoneNumber] = useState(true);
  const [isCodeSent, setIsCodeSent] = useState(false);
  const [confirmation, setConfirmation] = useState<ConfirmationResult | null>(
    null
  );
  const recaptchaVerifierRef = useRef<RecaptchaVerifier>();

  if (session) {
    redirect(ROUTE.DASHBOARD);
  }

  const handleSendCode = async () => {
    if (!mobileNumber.match("[1-9]{1}[0-9]{9}") || mobileNumber.length > 10) {
      setIsValidPhoneNumber(false);
      return;
    }
    if (!recaptchaVerifierRef.current)
      recaptchaVerifierRef.current = new RecaptchaVerifier(
        firebaseAuth,
        "recaptcha-container",
        {
          size: "invisible",
        }
      );
    try {
      const result = await signInWithPhoneNumber(
        firebaseAuth,
        CONSTANTS.PHONE_NUMBER_PREFIX + mobileNumber,
        recaptchaVerifierRef.current
      );
      setIsCodeSent(true);
      setConfirmation(result);
    } catch (error) {
      console.log("Error", error);
    }
  };
  const handleVerify = async () => {
    try {
      const response = await confirmation?.confirm(passkey);
      // console.log("response User", response?.user);
      if (response?.user) {
        const accessToken = await response.user.getIdToken();
        await addPhoneNumber(response.user.phoneNumber || "");
        await createSession(accessToken);
      }
    } catch (error) {
      console.log("Error", error);
    }
  };
  return (
    <div className="container mx-auto w-80">
      <h1 className="text-2xl text-center font-bold p-4">Login | CuspWeb</h1>
      <Input
        type="tel"
        prefixIcon={
          <Phone width="20px" height="20px" className="text-[#797878]" />
        }
        prefixText={CONSTANTS.PHONE_NUMBER_PREFIX}
        placeholder="Enter your number..."
        value={mobileNumber}
        onChange={(e) => {
          setMobileNumber(e.target.value);
          setIsValidPhoneNumber(true);
        }}
        disabled={isCodeSent}
      />
      {!isValidPhoneNumber && (
        <span className="text-sm text-red-400 pl-1">
          Please enter valid phone number!!
        </span>
      )}
      {!isCodeSent && (
        <div id="recaptcha-container" className="w-full mt-2"></div>
      )}
      <Button
        className="float-end w-full my-4"
        size="sm"
        onClick={handleSendCode}
        disabled={!isValidPhoneNumber || isCodeSent}
      >
        Send Code
      </Button>
      {isCodeSent && (
        <>
          <div className="text-sm mb-2">
            Enter your 6 digit TOTP Verification code here
          </div>
          <InputOtpComp passkey={passkey} setPasskey={setPasskey} />
          <Button
            className="mt-4 float-end w-full"
            size="sm"
            onClick={handleVerify}
            disabled={passkey.length !== 6}
          >
            Verify
          </Button>
        </>
      )}
    </div>
  );
}
