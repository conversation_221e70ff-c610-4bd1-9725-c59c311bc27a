import { defineConfig, devices } from '@playwright/test';

// Base URL for all tests
const baseURL = process.env.BASE_URL || 'http://localhost:3000';

/**
 * See https://playwright.dev/docs/test-configuration
 * Optimized configuration for comprehensive testing
 */
export default defineConfig({
  // Test directory
  testDir: './tests',

  // Run tests in files in parallel
  fullyParallel: true,

  // Fail the build on CI if you accidentally left test.only in the source code
  forbidOnly: !!process.env.CI,

  // Retry on CI only, but allow 1 retry in development
  retries: process.env.CI ? 2 : 1,

  // Opt out of parallel tests on CI
  workers: process.env.CI ? 1 : undefined,

  // Reporter to use
  reporter: [
    ['html', { open: 'never' }],
    ['list']
  ],

  // Shared settings for all projects
  use: {
    // Base URL to use in actions like `await page.goto('/')`
    baseURL,

    // Collect trace when retrying the failed test
    trace: 'on-first-retry',

    // Record video for failed tests
    video: 'on-first-retry',

    // Take screenshot on failure
    screenshot: 'only-on-failure',
  },

  // Configure projects for different scenarios
  projects: [
    // Page tests - Chromium
    {
      name: 'pages-chromium',
      use: { ...devices['Desktop Chrome'] },
      testMatch: /specs\/pages\/.*\.spec\.ts$/,
    },

    // Component tests - Chromium
    {
      name: 'components-chromium',
      use: { ...devices['Desktop Chrome'] },
      testMatch: /specs\/components\/.*\.spec\.ts$/,
    },

    // Visual tests - Chromium only
    {
      name: 'visual',
      use: { ...devices['Desktop Chrome'] },
      testMatch: /specs\/visual\/.*\.spec\.ts$/,
    },

    // Accessibility tests - Chromium only
    {
      name: 'accessibility',
      use: { ...devices['Desktop Chrome'] },
      testMatch: /specs\/accessibility\/.*\.spec\.ts$/,
    },

    // API tests - Chromium only
    {
      name: 'api',
      use: { ...devices['Desktop Chrome'] },
      testMatch: /specs\/api\/.*\.spec\.ts$/,
    },
  ],

  // Web server configuration
  webServer: [
    // Web app server
    {
      command: 'cd ../../apps/web && pnpm dev',
      url: baseURL,
      reuseExistingServer: !process.env.CI,
      stdout: 'pipe',
      stderr: 'pipe'
    },
  ],
});
