import type { <PERSON>a, StoryObj } from "@storybook/react";
import { RecurrentTransactionCard, RecurrentTransactionsTable } from "@repo/ui";
import {
  dummyRecurrentTransactions,
  creditCategories,
  debitCategories,
  dummyAccount,
} from "@repo/ui";
import camelcaseKeys from "camelcase-keys";

const camelizedTransactions = camelcaseKeys(dummyRecurrentTransactions, {
  deep: true,
});
const camelizedAccounts = camelcaseKeys(dummyAccount, { deep: true });

const enhancedTxns = camelizedTransactions.txns.map((txn) => {
  const matchingAccount = camelizedAccounts.accounts.find(
    (acc) => acc.accountId.trim() === txn.txnCard.accountId
  );

  if (matchingAccount) {
    const newTxnCard = {
      ...txn.txnCard,
      account: matchingAccount,
    };

    return {
      ...txn,
      txnCard: newTxnCard,
    };
  }

  return txn;
});

const enhancedTransactions = {
  summaries: camelizedTransactions.summaries,
  txns: enhancedTxns as RecurrentTransactionCard[],
};

const meta: Meta<typeof RecurrentTransactionsTable> = {
  title: "Recurrent Transaction/Recurrent Transactions Table",
  component: RecurrentTransactionsTable,
  tags: ["autodocs"],
  args: {
    recurrentTransactions: enhancedTransactions,
    categories: [...creditCategories, ...debitCategories],
    openTransactionId: undefined,
    onTransactionClick: () => {},
    onChangeReceiver: () => {},
    onAddEditTagClick: () => {},
  },
};
export default meta;
type Story = StoryObj<typeof RecurrentTransactionsTable>;

export const Default: Story = {};
