import { NavigationRail, NavigationRailItem } from "@repo/ui";
import type { Meta, StoryObj } from "@storybook/react";

import { items } from '@repo/ui/data/dummyNavigation';

const meta: Meta<typeof NavigationRail> = {
  title: "Components/Navigation Rail",
  parameters: {
    layout: 'fullscreen'
  },
  component: NavigationRail,
  decorators: [
    (Story) => (
      <div className="h-svh">
        <Story />
      </div>
    )
  ]
};

export default meta;

type Story = StoryObj<typeof meta>;

export const Default: Story = {
    args: {
        items: items,
        activeLink: "Transactions"
    }
};