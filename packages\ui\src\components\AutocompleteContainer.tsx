"use client";
import { useState, useMemo } from "react";
import { Autocomplete } from "./Autocomplete";

const optionsList: string[] = [
  "Swiggy instamart",
  "Swiggy grocery",
  "Swiggy delivery",
  "ICICI Bank",
];

export const AutocompleteContainer = () => {
  const [query, setQuery] = useState("");
  const [open, setOpen] = useState(false);
  const [options, setOptions] = useState(optionsList);

  const filteredOptions = useMemo(() => {
    return query.trim() === ""
      ? options
      : options.filter((option) =>
          option.toLowerCase().includes(query.toLowerCase())
        );
  }, [query, options]);

  const handleQueryChange = (value: string) => {
    setQuery(value);
  };

  const handleOptionSelect = () => {
    setQuery("");
    setOpen(false);
  };

  const handleCreateTag = () => {
    if (query && !options.includes(query)) {
      setOptions((prev) => [...prev, query]);
      setQuery("");
      setOpen(false);
    }
  };

  const handleClear = () => {
    const wasEmpty = query.length <= 0;
    setQuery("");

    if (wasEmpty) setOpen(false);
  };

  return (
    <Autocomplete
      query={query}
      options={filteredOptions}
      open={open}
      onQueryChange={handleQueryChange}
      onOptionSelect={handleOptionSelect}
      onClear={handleClear}
      onCreateTag={handleCreateTag}
      onOpenChange={setOpen}
    />
  );
};
