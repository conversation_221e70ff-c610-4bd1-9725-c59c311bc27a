import { requestMetadata, createGrpcClient } from "./utils/create-grpc-client";
import * as grpc from "@grpc/grpc-js";
import { GetAccessibleGroupsResponse, GetAccessibleUsersResponse } from "./member-mapper";
import { GRPC_CONFIG } from "./constants";

type UserServiceClient = grpc.Client & {
  GetAccessibleUsers: (
    request: object,
    metadata: grpc.Metadata,
    callback: (
      error: grpc.ServiceError | null,
      response: GetAccessibleUsersResponse
    ) => void
  ) => void;
  GetAccessibleGroups: (
    request: object,
    metadata: grpc.Metadata,
    callback: (
      error: grpc.ServiceError | null,
      response: GetAccessibleGroupsResponse
    ) => void
  ) => void;
};

const userClient: UserServiceClient = createGrpcClient<UserServiceClient>({
  protoPath: GRPC_CONFIG.PROTO_FILES.USER,
  servicePath: 'backend_services.user',
  serviceConstructor: 'User'
});

/**
 * Gets accessible users from the API
 * @param authToken The authentication token
 * @returns A promise that resolves to the accessible users response
 */
export function getAccessibleUsers(
  authToken: string
): Promise<GetAccessibleUsersResponse> {
  requestMetadata.set("authorization", authToken);

  return new Promise((resolve, reject) => {
    userClient.GetAccessibleUsers(
      {},
      requestMetadata,
      (error, response) => {
        if (error) {
          console.error("GetAccessibleUsers error:", error);
          reject(error);
        } else {
          resolve(response);
        }
      }
    );
  });
}

/**
 * Gets accessible groups from the API
 * @param authToken The authentication token
 * @returns A promise that resolves to the accessible groups response
 */
export function getAccessibleGroups(
  authToken: string
): Promise<GetAccessibleGroupsResponse> {
  requestMetadata.set("authorization", authToken);

  return new Promise((resolve, reject) => {
    userClient.GetAccessibleGroups(
      {},
      requestMetadata,
      (error, response) => {
        if (error) {
          console.error("GetAccessibleGroups error:", error);
          reject(error);
        } else {
          resolve(response);
        }
      }
    );
  });
}