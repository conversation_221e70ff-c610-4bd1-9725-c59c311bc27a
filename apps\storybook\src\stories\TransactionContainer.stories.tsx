import { Meta, StoryObj } from "@storybook/react";
import { dummyTransactions } from "@repo/ui";
import { TransactionsContainer } from "@repo/ui";

const meta: Meta<typeof TransactionsContainer> = {
  title: "Components/Transactions Container",
  component: TransactionsContainer,
  parameters: {
    layout: 'fullscreen'
  },
  args: {
    changeCategoryAction: () => Promise.resolve()
  },
  decorators: [
    (Story) => (
      <div className="h-svh bg-slate-100">
        <Story />
      </div>
    ),
  ],
};

export default meta;
type Story = StoryObj<typeof TransactionsContainer>;

//Transactions Available
export const WithTransactions: Story = {
  args: {
    transactions: dummyTransactions,
  },
};

//Case 2: No Transactions
export const NoTransactions: Story = {
  args: {
    transactions: [],
  },
};
