import { Page } from '@playwright/test';
import { StorybookPage } from '../models/StorybookPage';

/**
 * Options for running a test
 */
export interface TestRunOptions {
  /** The name of the test (for logging) */
  testName: string;
  /** The function to run */
  testFn: () => Promise<void>;
  /** The page object */
  page: Page;
  /** Base name for screenshots */
  screenshotBaseName: string;
}

/**
 * Run a test with error handling and screenshots
 * @param options - Test run options
 */
export async function runTest(options: TestRunOptions): Promise<void> {
  const { testName, testFn, page, screenshotBaseName } = options;

  try {
    // Run the test function
    await testFn();
  } catch (error) {
    console.error(`Error in ${testName}:`, error);
    
    // Take a screenshot of the current state
    await page.screenshot({ path: `./test-results/${screenshotBaseName}-error.png` });
    
    // Re-throw the error
    throw error;
  }
}

/**
 * Options for running a Storybook component test
 */
export interface StorybookTestOptions {
  /** The name of the test (for logging) */
  testName: string;
  /** The story ID to navigate to */
  storyId: string;
  /** The Storybook page object */
  storybookPage: StorybookPage;
  /** The page object */
  page: Page;
  /** Base name for screenshots */
  screenshotBaseName: string;
  /** The test function to run after setup */
  testFn: () => Promise<void>;
}

/**
 * Run a Storybook component test with proper setup and error handling
 * @param options - Storybook test options
 */
export async function runStorybookTest(options: StorybookTestOptions): Promise<void> {
  const { testName, storyId, storybookPage, page, screenshotBaseName, testFn } = options;

  try {
    // Navigate to the story
    await storybookPage.goto(storyId);
    
    // Wait for the page to load
    await storybookPage.waitForPageLoad();
    
    // Take a screenshot of the page
    await page.screenshot({ path: `./test-results/${screenshotBaseName}-page.png` });
    
    // Run the test function
    await testFn();
  } catch (error) {
    console.error(`Error in ${testName}:`, error);
    
    // Take a screenshot of the current state
    await page.screenshot({ path: `./test-results/${screenshotBaseName}-error.png` });
    
    // Re-throw the error
    throw error;
  }
}
