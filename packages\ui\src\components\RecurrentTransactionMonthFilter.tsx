import React from "react";
import { cn } from "../lib/utils";
import { Badge } from "./ui/badge";


export enum RecurrentTxnType {
  MISSED = "RECURRENT_TXN_TYPE_MISSED",
  UPCOMING = "RECURRENT_TXN_TYPE_UPCOMING",
  PAID = "RECURRENT_TXN_TYPE_PAID",
}

interface RecurrentTransactionMonthFilterProps {
  selected: RecurrentTxnType;
  onSelect: (filter: RecurrentTxnType) => void;
}

export const txnStatus: { label: string; value: RecurrentTxnType }[] = [
  { label: "Missed", value: RecurrentTxnType.MISSED },
  { label: "Upcoming", value: RecurrentTxnType.UPCOMING },
  { label: "Paid", value: RecurrentTxnType.PAID },
];

export const RecurrentTransactionMonthFilter: React.FC<
RecurrentTransactionMonthFilterProps
> = ({ selected, onSelect }) => (
  <div className="flex space-x-4 bg-white p-2 rounded-lg">
    {txnStatus.map((status) => (
      <Badge
        key={status.value}
        onClick={() => onSelect(status.value)}
        className={cn(
          "rounded-2xl px-4 py-2 h-auto justify-center text-sm",
          selected !== status.value &&
            "bg-white text-[#797878] font-normal border border-[#f6f6f6]"
        )}
        selected={selected === status.value}
      >
        {status.label}
      </Badge>
    ))}
  </div>
);
