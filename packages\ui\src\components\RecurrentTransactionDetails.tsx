"use client";

import { useState } from "react";
import { Switch } from "./ui/switch";
import { Badge } from "./ui/badge";
import { Textarea } from "./ui/textarea";
import SvgExcludedCashFlowV2 from "../icons/excluded-cash-flow-v2";
import SvgSavedTransactionV2 from "../icons/saved-transaction-v2";
import { BankIconMapping } from "./MemberGroupFilter";
import { Button } from "./ui/button";
import {
  RecurrentCategory,
  RecurrentTransactionCard,
  RecurrentTransactions,
} from "./RecurrentTransactionsTable";
import { categoryIconMap } from "./AddEditTag";
import { Misc } from "../icons/categories";
import { currencyFormatter } from "../lib/helper";
import { UpcomingTransaction } from "./UpcomingTransactions";
import { MissedTransaction } from "./MissedTransactions";
import { PaymentHistory } from "./PaymentHistory";
interface RecurrentTransactionDetailsProps {
  recurrentData: RecurrentTransactions;
  categories?: RecurrentCategory[];
  onClose?: () => void;
  onExcludedCashflowChanged?: (groupId: string, flag: boolean) => void;
  onSavedChanged?: (groupId: string, flag: boolean) => void;
  onNotesChanged?: (groupId: string, notes: string) => void;
  onAddEditTagClick?: (transaction: RecurrentTransactionCard) => void;
}
export const calculateTotal = (transactions: RecurrentTransactionCard[]) => {
  return transactions.reduce((sum, txn) => sum + txn.txnCard.amount, 0);
};

export const formatAmountDisplay = (amount: number) => {
  const formattedAmount = currencyFormatter.format(amount).split(".");
  const amountUnit = formattedAmount[0];
  const amountSubUnit = formattedAmount[1];

  return (
    <>
      <span className="text-base font-bold text-[#000000]">{amountUnit}.</span>
      <span className="text-base text-[#797878] font-normal">
        {amountSubUnit}
      </span>
    </>
  );
};

export const formatTotalAmount = (amount: number) => {
  const formattedAmount = currencyFormatter.format(amount).split(".");
  return `${formattedAmount[0]}.${formattedAmount[1]}`;
};
enum EDIT_MODE {
  NONE = "none",
  UPCOMING = "upcoming",
  MISSED = "missed",
  HISTORY = "history",
}

export function RecurrentTransactionDetails({
  recurrentData,
  categories,
  onExcludedCashflowChanged,
  onSavedChanged,
  onNotesChanged,
  onAddEditTagClick,
}: RecurrentTransactionDetailsProps) {
  const summary = recurrentData.summaries[0];
  const firstTxn = recurrentData.txns.find(
    (txn) => txn.txnCard.recurrentGroupId === summary?.recurrentGroupId
  );
  const matchedAccount = firstTxn?.txnCard?.account;

  const [isExcludedCashflow, setIsExcludedCashflow] = useState(
    summary?.excludeCashFlow || false
  );
  const [isSaved, setIsSaved] = useState(summary?.favorite || false);
  const [notes, setNotes] = useState("");
  const [editMode, setEditMode] = useState<EDIT_MODE>(EDIT_MODE.NONE);

  const upcomingTxns = recurrentData.txns.filter(
    (txn) => txn.recurrentTxnType === "RECURRENT_TXN_TYPE_UPCOMING"
  );
  const missedTxns = recurrentData.txns.filter(
    (txn) => txn.recurrentTxnType === "RECURRENT_TXN_TYPE_MISSED"
  );
  const paidTxns = recurrentData.txns.filter(
    (txn) => txn.recurrentTxnType === "RECURRENT_TXN_TYPE_PAID"
  );

  const handleOnChangedSaved = () => {
    if (onSavedChanged && summary) {
      onSavedChanged(summary.recurrentGroupId, !isSaved);
    }
    setIsSaved((prev) => !prev);
  };

  const handleOnChangedExcludedCashflow = () => {
    if (onExcludedCashflowChanged && summary) {
      onExcludedCashflowChanged(summary.recurrentGroupId, !isExcludedCashflow);
    }
    setIsExcludedCashflow((prev) => !prev);
  };

  const handleNotesChange = () => {
    if (onNotesChanged && summary) {
      onNotesChanged(summary.recurrentGroupId, notes);
    }
  };
  const toggleEditMode = (mode: EDIT_MODE) => {
    setEditMode((prev) => (prev === mode ? EDIT_MODE.NONE : mode));
  };

  const handleCompleteTransaction = (txnId: string) => {
    console.log("Completing transaction:", txnId);
  };

  const handleDeleteTransaction = (txnId: string) => {
    console.log("Deleting transaction:", txnId);
  };
  const selectedCategory = summary?.tag
    ? categories!.find((cat) => {
        return cat.id === summary.tag.categoryId;
      })
    : null;

  return (
    <div className="flex flex-col gap-4 bg-background p-5">
      <div className="p-4 flex items-center gap-2">
        <span className="font-semibold text-lg text-black">
          Transactions details
        </span>
      </div>
      <div className="flex flex-col items-center gap-10 border border-[#F4EEF9] rounded-2xl p-9 text-sm bg-gradient-to-t from-white to-[#FAF8FC]">
        <div className="flex flex-col items-center w-4/5 gap-5 text-sm">
          <span className="text-[#1E1E1E]">Paid to</span>
          <span className="text-primary font-semibold">
            {summary?.merchant.merchantName}
          </span>

          <div className="flex items-center gap-1 text-sm text-[#1E1E1E]">
            <span>Paid by</span>
            <span className="font-medium">{summary?.holderName}</span>
            <div className="flex items-center ml-2 gap-1">
              <BankIconMapping
                bankName={matchedAccount?.branch}
                className="w-4"
              />
              <span className="text-xs">
                **{matchedAccount?.maskedAccNumber?.slice(-4)}
              </span>
            </div>
          </div>
          {selectedCategory ? (
            <Badge
              selected={false}
              variant="default"
              startIcon={categoryIconMap[selectedCategory.name] || <Misc />}
              className="text-[#905BB5] cursor-pointer"
              onClick={() => onAddEditTagClick?.(firstTxn!)}
            >
              {selectedCategory.name}
            </Badge>
          ) : (
            <Button
              size="sm"
              variant="outline"
              className="px-4 py-1.5 font-semibold rounded-[10px]"
              onClick={() => onAddEditTagClick?.(firstTxn!)}
            >
              Add Tag
            </Button>
          )}
          <div className="w-full grid grid-cols-2 gap-2 mt-5 text-xs">
            <div className="flex flex-col gap-1">
              <span className="text-[#797878]">Schedule</span>
              <div className="font-medium">
                {summary?.frequency || "Monthly"}
                <div>{summary?.dates.join(", ") || "2nd, 7th, 9th & 10th"}</div>
              </div>
            </div>
            <div className="flex flex-col gap-1">
              <span className="text-[#797878]">Transaction mode</span>
              <div className="font-medium">
                {summary?.txnMode || "Fund Transfer"}
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Toggles */}
      <div className="flex flex-col border border-[#F4EEF9] rounded-2xl p-5 gap-4 text-xs font-medium">
        <div className="flex justify-between items-center">
          <label
            htmlFor="exclude-cashflow-switch"
            className="flex gap-2 items-center cursor-pointer"
          >
            <SvgExcludedCashFlowV2 className="size-5 text-primary" />
            <span>Exclude from cash flow</span>
          </label>
          <Switch
            id="exclude-cashflow-switch"
            checked={isExcludedCashflow}
            onCheckedChange={handleOnChangedExcludedCashflow}
          />
        </div>
        <div className="flex justify-between items-center">
          <label
            htmlFor="save-transaction-switch"
            className="flex gap-2 items-center cursor-pointer"
          >
            <SvgSavedTransactionV2 className="size-5 text-primary" />
            <span>Save Transaction</span>
          </label>
          <Switch
            id="save-transaction-switch"
            checked={isSaved}
            onCheckedChange={handleOnChangedSaved}
          />
        </div>
      </div>
      {/* Upcoming Transactions */}
      <UpcomingTransaction
        transactions={upcomingTxns}
        editMode={editMode === EDIT_MODE.UPCOMING}
        onToggleEditMode={() => toggleEditMode(EDIT_MODE.UPCOMING)}
        onCompleteTransaction={handleCompleteTransaction}
      />
        {/* Missed Transactions */}
      <MissedTransaction
        transactions={missedTxns}
        editMode={editMode === EDIT_MODE.MISSED}
        onToggleEditMode={() => toggleEditMode(EDIT_MODE.MISSED)}
        onCompleteTransaction={handleCompleteTransaction}
      />
      {/* Payment History */}
      <PaymentHistory
        transactions={paidTxns}
        editMode={editMode === EDIT_MODE.HISTORY}
        onToggleEditMode={() => toggleEditMode(EDIT_MODE.HISTORY)}
        onDeleteTransaction={handleDeleteTransaction}
      />

      {/* Notes */}
      <div className="flex flex-col border border-[#F4EEF9] rounded-2xl p-5 gap-4 text-xs font-medium pl-7">
        <label htmlFor="notes">Your Notes</label>
        <Textarea
          className="resize-none text-xs font-normal border-none p-0 focus:outline-none focus-visible:ring-offset-0 focus-visible:ring-0"
          id="notes"
          rows={5}
          placeholder="Please enter your notes here..."
          value={notes}
          onChange={(event) => setNotes(event.target.value)}
          onBlur={handleNotesChange}
        />
      </div>

      {/* Action Buttons */}
      <div className="px-4 mt-4">
        <button className="w-full bg-primary text-white py-3 rounded-3xl text-sm font-medium">
          Mark as completed
        </button>
        <div className="text-center mt-3">
          <button className="text-[#797878] text-sm">Delete</button>
        </div>
      </div>
    </div>
  );
}
