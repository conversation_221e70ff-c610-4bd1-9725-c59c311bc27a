import { Calendar } from "@repo/ui";
import { <PERSON>a, StoryObj } from "@storybook/react";
import { useState } from "react";
import { DateRange } from "react-day-picker";

const meta: Meta<typeof Calendar> = {
  component: Calendar,
  title: "Components/Calendar",
  tags: ["autodocs"],
};

export default meta;

type Story = StoryObj<typeof Calendar>;

const FAKE_DATE = new Date(2001, 9, 10);
const FAKE_DATE_RANGE = {
  from: new Date(2012, 5, 1),
  to: new Date(2012, 5, 15),
};

export const Default: Story = {
  render: function Render() {
    const [selectedDate, setSelectedDate] = useState<Date | undefined>(
      FAKE_DATE
    );

    return (
      <Calendar
        mode="single"
        value={selectedDate}
        onChange={(value: Date | undefined) => {
          setSelectedDate(value);
        }}
      />
    );
  },
};

export const RangePicker: Story = {
  render: function Render() {
    const [selectedRange, setSelectedRange] = useState<DateRange | undefined>(
      FAKE_DATE_RANGE
    );

    return (
      <Calendar
        mode="range"
        value={selectedRange}
        onChange={(value) => {
          setSelectedRange(value as DateRange | undefined);
        }}
      />
    );
  },
};

export const MonthView: Story = {
  args: {
    view: "month",
    mode: "single",
    value: FAKE_DATE,
  },
};

export const YearView: Story = {
  args: {
    view: "year",
    mode: "single",
    value: FAKE_DATE,
  },
};
