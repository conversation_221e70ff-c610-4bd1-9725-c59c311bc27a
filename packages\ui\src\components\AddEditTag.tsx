"use client";
import { ReactNode, useState, useEffect } from "react";
import { Button } from "./ui/button";
import { Category, TransactionCategory } from "./TransactionsTable";
import { CategorySelector } from "./categorySelector";
import { format } from "date-fns";
import {
  Fitness,
  FoodDrink,
  Insurance,
  Misc,
  PetCare,
  SelfTransfer,
  Subscription,
  Support,
  Tax,
  Income,
  Child,
  Travel,
  Medical,
  Clothing,
  Utility,
  BodyCare,
  Household,
  Fun,
  Service,
  PersonalAsset,
  Debt,
  PersonalGrowth,
  Investment,
} from "../icons/categories";
import { cn } from "../lib/utils";

export interface TransactionMultiSelectCategory {
  category_id: string;
  subcategory_id: string[];
}
interface AddEditTagProps {
  selectedCategory?: Category;
  categories: Category[];
  onSelectCategory: (category: TransactionMultiSelectCategory | TransactionMultiSelectCategory[]) => void;
  transaction?: {
    entity: string;
    amount: number;
    date: Date;
    narration?: string;
    tag?: TransactionCategory;
  };
}

// Keep Category names in alphabetical order
export const categoryIconMap: { [key: string]: ReactNode } = {
  "Body Care": <BodyCare />,
  "Child": <Child />,
  "Clothing": <Clothing />,
  "Debt": <Debt />,
  "Entertainment": <Misc />,
  "Fitness": <Fitness />,
  "Food & Drink": <FoodDrink />,
  "Fun": <Fun />,
  "Gifts": <Misc />,
  "Household": <Household />,
  "Income": <Income />,
  "Insurance": <Insurance />,
  "Investment": <Investment />,
  "Medical": <Medical />,
  "Misc": <Misc />,
  "Personal Asset": <PersonalAsset />,
  "Personal Growth": <PersonalGrowth />,
  "Pet Care": <PetCare />,
  "Recurrent Payment": <Utility />,
  "Restaurants": <Misc />,
  "Self Transfer": <SelfTransfer />,
  "Service": <Service />,
  "Services": <Misc />,
  "Subscription": <Subscription />,
  "Support": <Support />,
  "Tax": <Tax />,
  "Travel or Transportation": <Travel />,
};

const AddEditTag: React.FC<AddEditTagProps> = ({
  selectedCategory,
  categories,
  onSelectCategory,
  transaction,
}) => {
  const [currentSelected, setCurrentSelected] = useState<
    TransactionMultiSelectCategory | TransactionMultiSelectCategory[] | null
  >(null);

  const handleConfirmSelection = () => {
    if (currentSelected) {
      onSelectCategory(currentSelected);
    }
  };

  const handleRemoveTag = () => {
    onSelectCategory({
      category_id: '',
      subcategory_id: ['']
    });
    setCurrentSelected(null);
  };

  useEffect(() => {
    if (transaction?.tag) {
      setCurrentSelected({
        category_id: transaction.tag.category_id,
        subcategory_id: [transaction.tag.subcategory_id]
      })
    }
  }, [transaction])

  return (
    <div className="flex gap-4 flex-col bg-background h-[90vh] max-h-[90vh]">
      <div className="flex-1 px-[24px] pb-[16px]">
        {transaction && (
          <div className="bg-[#905BB5] text-white p-[19px] rounded-[10px] h-[144px] mb-4">
            <div className="flex justify-between items-center">
              <div className="mb-1 gap-1">
                <p className="font-[600]">{transaction.entity}</p>
                <p className="text-xs font-medium">
                  ₹{transaction.amount.toFixed(2)}{" "}
                  {transaction.date &&
                    ` | ${format(transaction.date, "MMM d ''yy")}`}
                </p>
              </div>
              <div className="bg-[#F4EEF9] text-[#905BB5] px-2 py-1 rounded-[10px] text-[11px] font-medium flex gap-2 justify-center items-center">
                <span>{categoryIconMap[selectedCategory?.name!] || <Misc />}</span>
                {selectedCategory?.name ?? "Add Tag"}
              </div>
            </div>
            {transaction?.narration && (
              <div>
                <div className="text-xs text-[#DBC8EA] mb-1">Narration</div>
                <span className="text-[10px] block max-w-[240px] font-medium break-words whitespace-pre-wrap">
                  {transaction.narration}
                </span>
              </div>
            )}
          </div>
        )}

        <div className="mt-[8px] overflow-hidden">
          <CategorySelector
            key={selectedCategory?.id}
            categories={categories}
            selectedCategories={currentSelected}
            onCategorySelectionChange={setCurrentSelected}
            mode="modal"
            multiSelection={false}
            showEveryCategories={false}
          />
        </div>
      </div>

      {currentSelected && (
        <div
          className={cn(
            "sticky bottom-0 bg-background px-[24px] py-[16px] border-t border-muted",
            selectedCategory ? "justify-between" : "justify-end",
            "flex"
          )}
        >
          {selectedCategory && <Button
            variant="outline"
            onClick={handleRemoveTag}
            className="text-red-500 border-red-200 h-[50px]"
          >
            Remove Tag
          </Button>}
          <Button
            variant="default"
            onClick={handleConfirmSelection}
            className="bg-[#905BB5] hover:bg-[#7D4DA3] text-white h-[50px]"
          >
            {selectedCategory ? "Save Changes" : "Add tag"}
          </Button>
        </div>
      )}
    </div>
  );
};

export { AddEditTag };
