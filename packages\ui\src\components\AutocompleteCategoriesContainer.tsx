"use client";
import { useState, useMemo, useEffect } from "react";
import { Badge } from "./ui/badge";
import { X } from "lucide-react";
import { Autocomplete } from "./Autocomplete";
import { categoryIconMap } from "./AddEditTag";
import { Category } from "./TransactionsTable";
interface AutocompleteCategoriesContainerProps {
  categories: Category[];
  selectedTags?: string[];
  onTagsChange?: (tags: string[]) => void;
}

export const AutocompleteCategoriesContainer: React.FC<
  AutocompleteCategoriesContainerProps
> = ({ categories, selectedTags, onTagsChange }) => {
  const optionsList = useMemo(() => {
    return categories.map((cat) => cat.name);
  }, [categories]);

  const [query, setQuery] = useState("");
  const [open, setOpen] = useState(false);
  const [selectedCategories, setSelectedCategories] = useState<string[]>(
    selectedTags || []
  );
  useEffect(() => {
    if (selectedTags) {
      setSelectedCategories(selectedTags);
    }
  }, [selectedTags]);
  useEffect(() => {
    if (onTagsChange) {
      onTagsChange(selectedCategories);
    }
  }, [selectedCategories, onTagsChange]);

  const filteredOptions = useMemo(() => {
    return query.trim() === ""
      ? optionsList
      : optionsList.filter((option) =>
          option.toLowerCase().includes(query.toLowerCase())
        );
  }, [query, optionsList]);

  const handleQueryChange = (value: string) => {
    setQuery(value);
  };

  const handleOptionSelect = (option: string) => {
    if (!selectedCategories.includes(option)) {
      const newSelectedCategories = [...selectedCategories, option];
      setSelectedCategories(newSelectedCategories);
      if (onTagsChange) {
        onTagsChange(newSelectedCategories);
      }
    }
    setQuery("");
    setOpen(false);
  };

  const handleRemoveCategory = (cat: string) => {
    const newSelectedCategories = selectedCategories.filter(
      (item) => item !== cat
    );
    setSelectedCategories(newSelectedCategories);
    if (onTagsChange) {
      onTagsChange(newSelectedCategories);
    }
  };

  const handleClear = () => {
    setQuery("");
    setOpen(false);
  };

  return (
    <div>
      {selectedCategories.length > 0 && (
        <div className="flex flex-wrap gap-2 mb-2">
          {selectedCategories.map((cat) => (
            <Badge key={cat} selected={true}>
              <div className="flex gap-2">
                <span>
                  {categoryIconMap[cat]}
                  {cat}
                </span>
                <span className="mt-0.5">
                  <X
                    className="ml-2 h-3 w-3"
                    onClick={() => handleRemoveCategory(cat)}
                  />
                </span>
              </div>
            </Badge>
          ))}
        </div>
      )}
      <Autocomplete
        query={query}
        options={filteredOptions}
        open={open}
        onQueryChange={handleQueryChange}
        onOptionSelect={handleOptionSelect}
        onClear={handleClear}
        onOpenChange={setOpen}
        isCategory={true}
      />
    </div>
  );
};
