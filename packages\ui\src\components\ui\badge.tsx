import * as React from "react";
import { cva, type VariantProps } from "class-variance-authority";

import { cn } from "../../lib/utils";

const badgeVariants = cva(
  "inline-flex items-center rounded-[10px] h-8 px-4 py-1 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 cursor-pointer gap-1 border-transparent min-w-20",
  {
    variants: {
      variant: {
        default: "cursor-pointer bg-[#F4EEF9] text-[#AC80CC] hover:bg-primary/15",
      },
    },
    defaultVariants: {
      variant: "default",
    },
  }
);

export interface BadgeProps
  extends React.HTMLAttributes<HTMLDivElement>,
    VariantProps<typeof badgeVariants> {
  startIcon?: React.ReactNode;
  selected: boolean;
}

function Badge({
  className,
  variant,
  startIcon,
  selected,
  children,
  ...props
}: BadgeProps) {
  return (
    <div
      className={cn(
        badgeVariants({ variant }),
        selected && "bg-primary text-primary-foreground hover:bg-primary/85",
        className
      )}
      {...props}
    >
      {startIcon && (
        <span
          className="flex items-center justify-center w-6 h-6">
          {startIcon}
        </span>
      )}
      <span className="truncate">{children}</span>
    </div>
  );
}

export { Badge, badgeVariants };
