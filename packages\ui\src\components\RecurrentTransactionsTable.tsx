"use client";

import { format } from "date-fns";
import {
  Table,
  TableRow,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
} from "./ui/table";
import { categoryIconMap } from "./AddEditTag";
import { ReceiverInput } from "./ReceiverInput";
import { cn } from "../lib/utils";
import { currencyFormatter } from "../lib/helper";
import SvgExcludedCashFlow from "../icons/excluded-cash-flow";
import SvgSavedTransaction from "../icons/saved-transaction";
import { Badge } from "./ui/badge";
import { Button } from "./ui/button";
import { Misc } from "../icons/categories";
import { BankIconMapping } from "./MemberGroupFilter";
import { RecurrentTxnType } from "./RecurrentTransactionMonthFilter";
import { useMemo } from "react";

interface TransactionCategory {
  categoryCollection: string;
  categoryId: string;
  subcategoryId: string;
}

export interface RecurrentTransactionSummary {
  dates: string[];
  recurrentGroupId: string;
  merchant: {
    userId: string;
    merchantId: string;
    merchantName: string;
  };
  tag: TransactionCategory;
  accountId: string;
  userId: string;
  amount: number;
  frequency: string;
  favorite: boolean;
  excludeCashFlow: boolean;
  txnMode: string;
  holderName?: string;
}
interface Account {
  accountId: string;
  linkedAccRef: string;
  maskedAccNumber: string;
  fipId: string;
  holderName: string;
  branch: string;
  fiType: string;
  userId: string;
  dataSyncedAt: string;
}
export interface RecurrentTransactionCard {
  recurrentTxnType: string;
  txnCard: {
    txnId: string;
    accountId: string;
    amount: number;
    type: string;
    txnTimestamp: string;
    mode: string;
    narration: string;
    rawTxnId: string;
    favorite: boolean;
    tag: TransactionCategory | null;
    merchant: {
      userId: string;
      merchantId: string;
      merchantName: string;
    } | null;
    excludeCashFlow: boolean;
    fipId: string;
    userNotes: string;
    cashFlowPeriod: { month: number; year: number } | null;
    documentsCount: number;
    recurrentGroupId: string;
    account?: Account;
  };
}
interface Subcategory {
  id: string;
  name: string;
  sortKey: number;
  createdAt: number;
}
export interface RecurrentCategory {
  id: string;
  name: string;
  subCategories: Subcategory[];
  sortKey: number;
  createdAt: number;
  sfSymbol?: string;
  iconUrl?: string;
  cashflowGroup?: number;
}

export interface RecurrentTransactions {
  summaries: RecurrentTransactionSummary[];
  txns: RecurrentTransactionCard[];
}

interface DateGroup {
  date: Date;
  transactions: RecurrentTransactionCard[];
}

interface RecurrentTransactionsTableProps {
  recurrentTransactions: RecurrentTransactions;
  categories: RecurrentCategory[];
  openTransactionId: string | undefined;
  onTransactionClick: (transaction: RecurrentTransactionCard) => void;
  onChangeReceiver: (transactionId: string, receiver: string) => void;
  onAddEditTagClick: (transaction: RecurrentTransactionCard) => void;
  filterType?: RecurrentTxnType;
}
const getFrequencyAbbreviation = (frequency: string): string => {
  if (!frequency) return "";

  const lowerFreq = frequency.toLowerCase();
  if (lowerFreq.includes("year")) return "Yr";
  if (lowerFreq.includes("month")) return "M";
  if (lowerFreq.includes("quarter")) return "Qtr";

  return lowerFreq.charAt(0).toUpperCase();
};

const getDateFromTimestamp = (timestamp: string): Date => {
  if (!timestamp) {
    return new Date();
  }
  return new Date(parseInt(timestamp) * 1000);
};
const RecurrentTransactionsTable = ({
  recurrentTransactions,
  categories,
  openTransactionId,
  onTransactionClick,
  onChangeReceiver,
  onAddEditTagClick,
  filterType,
}: RecurrentTransactionsTableProps) => {
  function handleTransactionClick(transaction: RecurrentTransactionCard) {
    onTransactionClick(transaction);
  }

  function handleAddEditTagClick(transaction: RecurrentTransactionCard) {
    onAddEditTagClick(transaction);
  }

  const findSummary = (groupId: string) => {
    return recurrentTransactions.summaries.find(
      (summary) => summary.recurrentGroupId === groupId
    );
  };

  const filteredTransactions = useMemo(() => {
    return filterType
      ? recurrentTransactions.txns.filter(
          (txn) => txn.recurrentTxnType === filterType
        )
      : recurrentTransactions.txns;
  }, [recurrentTransactions.txns, filterType]);

  const dateGroups = useMemo(() => {
    const groups: DateGroup[] = [];
    filteredTransactions.forEach((txn) => {
      const date = getDateFromTimestamp(txn?.txnCard?.txnTimestamp);
      const dayDate = new Date(
        date.getFullYear(),
        date.getMonth(),
        date.getDate()
      );

      const existingGroup = groups.find(
        (group) => group.date.getTime() === dayDate.getTime()
      );

      if (existingGroup) {
        existingGroup.transactions.push(txn);
      } else {
        groups.push({
          date: dayDate,
          transactions: [txn],
        });
      }
    });
    return groups.sort((a, b) => b.date.getTime() - a.date.getTime());
  }, [filteredTransactions]);
  dateGroups.sort((a, b) => b.date.getTime() - a.date.getTime());

  return (
    <Table className="bg-background mb-10">
      <TableHeader>
        <TableRow className="sr-only">
          <TableHead>Bank</TableHead>
          <TableHead>User</TableHead>
          <TableHead>Entity</TableHead>
          <TableHead>Amount</TableHead>
          <TableHead>Category</TableHead>
          <TableHead>Extra Info</TableHead>
        </TableRow>
      </TableHeader>

      {dateGroups.map((group, groupIndex) => (
        <TableBody key={groupIndex}>
          <TableRow className="hover:bg-transparent">
            <TableHead className="pt-6 pl-4" scope="rowgroup" colSpan={6}>
              <time
                dateTime={group.date.toISOString()}
                className="flex gap-1 items-baseline"
              >
                <span className="text-xl font-semibold text-[#1B1B1B]">
                  {format(group.date, "dd")}
                </span>
                <span className="text-xs font-medium text-[#797878]">
                  {format(group.date, "MMM'' yy")}
                </span>
              </time>
            </TableHead>
          </TableRow>

          {group.transactions.map((transaction) => {
            const formattedAmount = currencyFormatter
              .format(transaction.txnCard.amount)
              .split(".");
            const amountUnit = formattedAmount[0] + ".";
            const amountSubUnit = formattedAmount[1];

            const summary = findSummary(transaction.txnCard.recurrentGroupId);

            const selectedCategory = transaction.txnCard.tag
              ? categories.find(
                  (item) => item.id === transaction.txnCard.tag?.categoryId
                )
              : summary &&
                categories.find((item) => item.id === summary.tag.categoryId);

            const merchantName =
              transaction.txnCard.merchant?.merchantName ||
              (summary ? summary.merchant.merchantName : "Unknown");

            return (
              <TableRow
                key={
                  transaction.txnCard.txnId ||
                  `${transaction.txnCard.recurrentGroupId}-${transaction.recurrentTxnType}-${group.date.getTime()}`
                }
                className={cn(
                  "bg-white hover:bg-[#FAF8FC]",
                  openTransactionId === transaction.txnCard.txnId &&
                    "bg-[#FAF8FC]"
                )}
                onClick={() => handleTransactionClick(transaction)}
              >
                <TableCell className="pl-4 w-6">
                  <div className="flex items-center justify-start">
                    <BankIconMapping
                      bankName={transaction.txnCard.account?.branch!}
                      className="w-4"
                    />
                  </div>
                </TableCell>
                <TableCell className="px-3 text-base font-medium">
                  {transaction.txnCard.account ? (
                    transaction.txnCard.account.holderName
                  ) : (
                    <span className="text-sm text-disabled text-gray-600 text-nowrap">
                      John Doe
                    </span>
                  )}
                </TableCell>
                <TableCell className="px-3 w-[1%]">
                  <div
                    className={cn(
                      "flex font-bold items-baseline justify-end",
                      transaction.txnCard.type === "CREDIT" && "text-[#4FA33F]"
                    )}
                  >
                    {transaction.txnCard.type === "CREDIT" && (
                      <span className="mr-1" aria-hidden={true}>
                        +
                      </span>
                    )}
                    <span className="text-sm">{amountUnit}</span>
                    <span
                      className={cn(
                        "text-xs font-normal",
                        transaction.txnCard.type === "CREDIT"
                          ? "text-[#6FBE5D]"
                          : "text-[#888888]"
                      )}
                    >
                      {amountSubUnit}
                    </span>
                    {summary && (
                      <span className="text-[10px] font-medium text-[#905BB5] ml-1">
                        ({getFrequencyAbbreviation(summary.frequency)})
                      </span>
                    )}
                  </div>
                </TableCell>
                <TableCell className="px-3 pl-6">
                  <div className="flex flex-col">
                    <ReceiverInput
                      defaultValue={merchantName}
                      onChangeReceiver={(receiver) =>
                        transaction.txnCard.txnId &&
                        onChangeReceiver(transaction.txnCard.txnId, receiver)
                      }
                    />
                  </div>
                </TableCell>
                <TableCell
                  className="px-3 flex"
                  onClick={(event) => {
                    event.stopPropagation();
                    handleAddEditTagClick(transaction);
                  }}
                >
                  {selectedCategory ? (
                    <Badge
                      variant="default"
                      className="cursor-pointer"
                      selected={false}
                    >
                      <div className="flex gap-2 items-center justify-center">
                        <span className="text-sm">
                          {categoryIconMap[selectedCategory.name] || <Misc />}
                        </span>
                        <span className="text-sm font-semibold text-[#AC80CC]">
                          {selectedCategory.name}
                        </span>
                      </div>
                    </Badge>
                  ) : (
                    <Button
                      size="sm"
                      className="px-4 font-semibold rounded-[10px]"
                    >
                      Add Tag
                    </Button>
                  )}
                </TableCell>
                <TableCell className="min-w-20 max-w-20 relative">
                  <div className="absolute right-0 -top-1.5 flex gap-2 items-center justify-end pr-3">
                    {(transaction.txnCard.excludeCashFlow ||
                      (summary && summary.excludeCashFlow)) && (
                      <div>
                        <SvgExcludedCashFlow className="w-4" />
                        <span className="sr-only">Excluded from cash flow</span>
                      </div>
                    )}
                    {(transaction.txnCard.favorite ||
                      (summary && summary.favorite)) && (
                      <div>
                        <SvgSavedTransaction className="w-4 text-[#C4A4DC]" />
                        <span className="sr-only">Saved</span>
                      </div>
                    )}
                  </div>
                </TableCell>
              </TableRow>
            );
          })}
        </TableBody>
      ))}
    </Table>
  );
};

export { RecurrentTransactionsTable };
