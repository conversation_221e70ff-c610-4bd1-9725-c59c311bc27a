"use client";
import { useState } from "react";

import {
  RecurrentTransactions,
  RecurrentTransactionsTable,
  RecurrentCategory,
} from "./RecurrentTransactionsTable";
import { RecurrentTransactionMonthFilter, RecurrentTxnType } from "./RecurrentTransactionMonthFilter";

interface RecurrentTxnContainerProps {
  transactions: RecurrentTransactions;
  categories: RecurrentCategory[];
}
export const RecurrentTxnContainer: React.FC<RecurrentTxnContainerProps> = ({
  transactions,
  categories,
}) => {
  const [filter, setFilter] = useState<RecurrentTxnType>(
    RecurrentTxnType.UPCOMING
  );

  return (
    <div>
      <div className=" w-full">
        <div className=" flex items-center justify-center">
          <RecurrentTransactionMonthFilter
            selected={filter}
            onSelect={setFilter}
          />
        </div>

        <RecurrentTransactionsTable
          recurrentTransactions={transactions}
          openTransactionId={undefined}
          categories={categories}
          onTransactionClick={() => {}}
          onChangeReceiver={() => {}}
          onAddEditTagClick={() => {}}
          filterType={filter}
        />
      </div>
    </div>
  );
};
