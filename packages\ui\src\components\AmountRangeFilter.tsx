"use client";
import React, { useEffect, useState } from "react";
import { CustomTooltip } from "./CustomTooltip";
import { RangeSlider } from "./RangeSlider";
import { formatINRCurrency } from "../lib/utils";
import { Label } from "./ui/label";
import { Input } from "./ui/input";

interface AmountRangeFilterProps {
  values: number[];
  onValueChange: React.Dispatch<React.SetStateAction<number[]>>;
  min?: number;
  max?: number;
  step?: number;
}

const RangeTooltip = ({ value }: { value?: string | number }) => {
  const numericValue = typeof value === "string" ? Number(value) : value;

  const displayValue =
    numericValue && numericValue >= 200000
      ? `${formatINRCurrency(200000)}+`
      : formatINRCurrency(numericValue || 0);

  return <CustomTooltip value={displayValue} />;
};

const PrefixIcon = () => (
  <div className="text-[#C4C4C4] text-sm flex gap-2">
    <span>₹</span>
    <span>|</span>
  </div>
);

function AmountRangeFilter({
  values,
  onValueChange,
  min = 0,
  max = 100000,
  step = 100,
}: AmountRangeFilterProps) {
  const [localValues, setLocalValues] = useState<(number | "")[]>(values);
  const [errors, setErrors] = useState<{ min?: string; max?: string }>({});
  const [sliderValues, setSliderValues] = useState<number[]>(values);
  useEffect(() => {
    setLocalValues(values);
    setSliderValues(values);
  }, [values]);

  const validateAmount = (amount: number | "", isMin: boolean) => {
    if (amount === "") {
      return { isValid: true, error: undefined };
    }

    const numAmount = typeof amount === "string" ? Number(amount) : amount;

    if (isNaN(numAmount)) {
      return { isValid: false, error: "Enter a valid amount" };
    }

    if (isMin) {
      if (numAmount < min) {
        return { isValid: false, error: "Min must be greater than 0" };
      }
      if (numAmount > (localValues[1] as number)) {
        return { isValid: false, error: "Min amount cannot exceed max amount" };
      }
    } else {
      if (numAmount < (localValues[0] as number)) {
        return { isValid: false, error: "Max amount cannot be less than min amount" };
      }
    }

    return { isValid: true, error: undefined };
  };

  const handleMinInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    const newMin = value === "" ? "" : Number(value);
    const validation = validateAmount(newMin, true);
    const newErrors = { ...errors };

    if (!validation.isValid) {
      newErrors.min = validation.error;
    } else {
      newErrors.min = undefined;
      const updated: number[] = [newMin as number, localValues[1] as number];
      setSliderValues(updated);
      onValueChange(updated);
    }

    setErrors(newErrors);
    setLocalValues([newMin, localValues[1]!]);
  };

  const handleMaxInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    const newMax = value === "" ? "" : Number(value);
    const validation = validateAmount(newMax, false);
    const newErrors = { ...errors };

    if (!validation.isValid) {
      newErrors.max = validation.error;
    } else {
      newErrors.max = undefined;
      const updated: number[] = [localValues[0]! as number, newMax as number];
      setSliderValues(updated);
      onValueChange(updated);
    }

    setErrors(newErrors);
    setLocalValues([localValues[0]!, newMax]);
  };

  return (
    <div className="w-full px-3">
      <p className="text-sm leading-10 mb-12">Amount</p>
      <div className="mr-3">
        <RangeSlider
          label={(value) => <RangeTooltip value={value} />}
          value={sliderValues}
          onValueChange={(vals) => {
            setSliderValues(vals);
            setLocalValues(vals);
            onValueChange(vals); 
          }}
          min={min}
          max={max}
          step={step}
        />
      </div>

      <div className="flex gap-2 justify-between my-2 items-start mb-3">
        <div className="flex flex-col gap-1.5 w-1/2" key="minAmt">
          <Label htmlFor="minAmtInput" className="text-sm ">
            Min Amt
          </Label>
          <div className="relative w-full">
            <Input
              id="minAmtInput"
              value={localValues[0]}
              className="w-full"
              outerClassName={`rounded-lg shadow-none ${
                errors.min ? "border-red-500" : ""
              }`}
              prefixIcon={<PrefixIcon />}
              type="number"
              onChange={handleMinInputChange}
              min={0}
              max={localValues[1]}
            />
            {errors.min && (
              <span className="text-xs text-red-500 min-h-[18px] block mt-1">
                {errors.min || ""}
              </span>
            )}
          </div>
        </div>
        <div
          className={`w-3 border-t border-[#C4C4C4]  ${errors.max || errors.min ? "mt-12" : "mt-10"}`}
        />
        <div className="flex flex-col gap-1.5 w-1/2" key="maxAmt">
          <Label htmlFor="maxAmtInput" className="text-sm ">
            Max Amt
          </Label>
          <div className="relative w-full">
            <Input
              id="maxAmtInput"
              value={localValues[1]}
              className="w-full"
              outerClassName={`rounded-lg shadow-none ${
                errors.max ? "border-red-500" : ""
              }`}
              prefixIcon={<PrefixIcon />}
              type="number"
              onChange={handleMaxInputChange}
              min={0}
            />
            {errors.max && (
              <span className="text-xs text-red-500 min-h-[18px] block mt-1">
                {errors.max || ""}
              </span>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}

export { AmountRangeFilter };
