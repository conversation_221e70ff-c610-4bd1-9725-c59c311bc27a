"use client";
import React, { useMemo } from "react";
import { Category } from "./TransactionsTable";
import { CategorySelector } from "./categorySelector";

interface CategoryFilterProps {
  categories: Category[];
  selectedCategories: { [categoryId: string]: string[] };
  setSelectedCategories: React.Dispatch<
    React.SetStateAction<{ [categoryId: string]: string[] }>
  >;
}

export const CategoryFilter = ({
  categories,
  selectedCategories,
  setSelectedCategories,
}: CategoryFilterProps) => {
  
  const transformedSelectedCategories = useMemo(() => {
    if (!selectedCategories || Object.keys(selectedCategories).length === 0) {
      return null;
    }

    const transformed = Object.entries(selectedCategories)
      .filter(([_, subcategoryIds]) => {
        return Array.isArray(subcategoryIds) && subcategoryIds.length > 0;
      })
      .map(([categoryId, subcategoryIds]) => ({
        category_id: categoryId,
        subcategory_id: Array.isArray(subcategoryIds) ? subcategoryIds : [],
      }));

    return transformed.length > 0 ? transformed : null;
  }, [selectedCategories]);

  const handleCategorySelectionChange = (
    selection:
      | { category_id: string; subcategory_id: string[] }
      | { category_id: string; subcategory_id: string[] }[]
      | null
  ) => {
    if (!selection) {
      setSelectedCategories({});
      return;
    }

    const updated = { ...selectedCategories };
    const selectionArray = Array.isArray(selection) ? selection : [selection];
    selectionArray.forEach(({ category_id, subcategory_id }) => {
      const subcategoryArray = Array.isArray(subcategory_id) 
        ? subcategory_id 
        : [subcategory_id as unknown as string];
      
      if (subcategoryArray.length > 0) {
        updated[category_id] = subcategoryArray;
      } else {
        delete updated[category_id];
      }
    });

    setSelectedCategories(updated);
  };

  return (
    <div>
      <h3 className="text-sm font-medium mb-3">Categories</h3>
      <CategorySelector
        categories={categories}
        selectedCategories={transformedSelectedCategories}
        onCategorySelectionChange={handleCategorySelectionChange}
        mode="filter"
        multiSelection={true}
        showEveryCategories={false}
      />
    </div>
  );
};