import type { <PERSON>a, StoryObj } from "@storybook/react";
import { TransactionsTable } from "@repo/ui";
import { creditCategories, debitCategories, dummyTransactions } from "@repo/ui";

const meta: Meta<typeof TransactionsTable> = {
  title: "Components/Transactions Table",
  component: TransactionsTable,
  tags: ["autodocs"],
  args: {
    transactionGroups: dummyTransactions,
    categories: [...creditCategories, ...debitCategories]
  },
};

export default meta;
type Story = StoryObj<typeof TransactionsTable>;

export const Default: Story = {};
