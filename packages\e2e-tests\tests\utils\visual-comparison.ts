import { expect, Page, Locator } from '@playwright/test';
import fs from 'fs';
import path from 'path';
import pixelmatch from 'pixelmatch';
import { PNG } from 'pngjs';

/**
 * Options for visual comparison
 */
export interface VisualComparisonOptions {
  /** Maximum allowed pixel difference (percentage) */
  threshold?: number;
  /** Directory to store snapshots */
  snapshotDir?: string;
  /** Whether to update snapshots */
  updateSnapshots?: boolean;
}

/**
 * Compare a screenshot with a baseline image
 * @param screenshotBuffer - Buffer containing the screenshot
 * @param baselinePath - Path to the baseline image
 * @param options - Comparison options
 */
export async function compareScreenshots(
  screenshotBuffer: Buffer,
  baselinePath: string,
  options: VisualComparisonOptions = {}
): Promise<{ diffPercentage: number; diffPath: string | null }> {
  const {
    threshold = 0.1,
    snapshotDir = './tests/visual/snapshots',
    updateSnapshots = process.env.UPDATE_SNAPSHOTS === 'true',
  } = options;

  // Ensure snapshot directory exists
  if (!fs.existsSync(snapshotDir)) {
    fs.mkdirSync(snapshotDir, { recursive: true });
  }

  // If baseline doesn't exist or we're updating snapshots, save the current screenshot as baseline
  if (updateSnapshots || !fs.existsSync(baselinePath)) {
    fs.writeFileSync(baselinePath, screenshotBuffer);
    return { diffPercentage: 0, diffPath: null };
  }

  // Load images
  const baselineBuffer = fs.readFileSync(baselinePath);
  const baseline = PNG.sync.read(baselineBuffer);
  const screenshot = PNG.sync.read(screenshotBuffer);

  // Check dimensions
  if (baseline.width !== screenshot.width || baseline.height !== screenshot.height) {
    throw new Error(
      `Screenshot dimensions (${screenshot.width}x${screenshot.height}) don't match baseline (${baseline.width}x${baseline.height})`
    );
  }

  // Create diff image
  const diff = new PNG({ width: baseline.width, height: baseline.height });
  const numDiffPixels = pixelmatch(
    baseline.data,
    screenshot.data,
    diff.data,
    baseline.width,
    baseline.height,
    { threshold: 0.1 }
  );

  // Calculate diff percentage
  const diffPercentage = (numDiffPixels / (baseline.width * baseline.height)) * 100;

  // If diff is above threshold, save diff image
  let diffPath = null;
  if (diffPercentage > threshold) {
    diffPath = baselinePath.replace(/\.png$/, '.diff.png');
    fs.writeFileSync(diffPath, PNG.sync.write(diff));
  }

  return { diffPercentage, diffPath };
}

/**
 * Take a screenshot and compare it with a baseline
 * @param page - Playwright page
 * @param name - Screenshot name
 * @param options - Comparison options
 */
export async function expectScreenshotMatch(
  page: Page,
  name: string,
  options: VisualComparisonOptions = {}
): Promise<void> {
  const { snapshotDir = './tests/visual/snapshots' } = options;
  
  // Take screenshot
  const screenshotBuffer = await page.screenshot();
  
  // Define baseline path
  const baselinePath = path.join(snapshotDir, `${name}.png`);
  
  // Compare with baseline
  const { diffPercentage, diffPath } = await compareScreenshots(
    screenshotBuffer,
    baselinePath,
    options
  );
  
  // Assert that diff percentage is below threshold
  expect(
    diffPercentage,
    `Screenshot differs from baseline by ${diffPercentage.toFixed(2)}%. See diff at ${diffPath}`
  ).toBeLessThanOrEqual(options.threshold || 0.1);
}

/**
 * Take a screenshot of an element and compare it with a baseline
 * @param locator - Playwright locator
 * @param name - Screenshot name
 * @param options - Comparison options
 */
export async function expectElementScreenshotMatch(
  locator: Locator,
  name: string,
  options: VisualComparisonOptions = {}
): Promise<void> {
  const { snapshotDir = './tests/visual/snapshots' } = options;
  
  // Take screenshot
  const screenshotBuffer = await locator.screenshot();
  
  // Define baseline path
  const baselinePath = path.join(snapshotDir, `${name}.png`);
  
  // Compare with baseline
  const { diffPercentage, diffPath } = await compareScreenshots(
    screenshotBuffer,
    baselinePath,
    options
  );
  
  // Assert that diff percentage is below threshold
  expect(
    diffPercentage,
    `Element screenshot differs from baseline by ${diffPercentage.toFixed(2)}%. See diff at ${diffPath}`
  ).toBeLessThanOrEqual(options.threshold || 0.1);
}
