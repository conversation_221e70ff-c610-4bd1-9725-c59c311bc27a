"use client";

import { TransactionsContainer } from "./TransactionsContainer";
import { dummyTransactions } from "../data/dummyData";
import { Badge } from "./ui/badge";
import { FilterBadges } from "./FilterBadges";
import { useCallback, useState } from "react";
import { TransactionSearch } from "./TransactionSearch";
import { ExcludedCashFlowV2, GroupIcon } from "../icons";
import { AppSidebar } from "./AppSidebar";

export function HomePage() {
  const [isFilterOpen, setIsFilterOpen] = useState(false);
  const [filterBadges, setFilterBadges] = useState<string[]>([]);
  const [clearFilterFunction, setClearFilterFunction] = useState<
    ((value: string | undefined) => void) | null
  >(null);
  const [searchTerm, setSearchTerm] = useState<String | null>();

  const handleFilterToggle = () => {
    setIsFilterOpen((prev) => !prev);
  };
  
  const handleClearFilters = useCallback((fn: (value: string) => void) => {
    setClearFilterFunction(() => fn);
  }, []);

  return (
    <AppSidebar username={"John Doe"} email={"<EMAIL>"}>
      <div className="h-svh flex bg-slate-200">
        <div className="flex flex-col w-full">
          <div className="flex w-full items-start justify-between p-3 mt-6">
            <div className="w-4/5 place-items-center ml-[11rem]">
              <TransactionSearch
                onSearch={(query) => setSearchTerm(query)}
                onSearchCategoryChange={(category) =>
                  console.log("Selected badge:", category)
                }
              />
            </div>
            <div className="flex gap-5 w-1/5 justify-end mr-4">
              <Badge
                selected={false}
                className="bg-[#BDE4B2] py-2 px-[10.2px] rounded-[100px]"
              >
                <div className="flex gap-2 items-center">
                  <span className="text-xs text-[#2A5024]">Cashflow</span>
                  <ExcludedCashFlowV2 className="w-6 h-6" fill="#2A5024" />
                </div>
              </Badge>
              <div
                className="w-[29.9px] h-[29.9px] bg-[#DFECFF] rounded-[78.371px] flex items-center justify-center cursor-pointer"
                onClick={handleFilterToggle}
              >
                <GroupIcon />
              </div>
            </div>
          </div>

          <div className="px-4 mt-2 w-full">
            {filterBadges.length > 0 && (
              <FilterBadges
                badges={filterBadges}
                onRemoveFilter={(value) => {
                  if (clearFilterFunction) {
                    clearFilterFunction(value);
                  }
                }}
              />
            )}
          </div>

          <TransactionsContainer
            transactions={dummyTransactions}
            changeCategoryAction={() => Promise.resolve()}
            updateTransaction={() => {}}
            fetchTransactionDocuments={() => Promise.resolve({ documents: [] })}
            deleteTransactionDocuments={() => Promise.resolve({ ok: true })}
            defaultFilterState={isFilterOpen}
            onFilterStateChange={setIsFilterOpen}
            onFilterBadgesChange={setFilterBadges}
            onClearFilterFunc={handleClearFilters}
            lastItemRef={() => {}}
            hasMore={false}
          />
        </div>
      </div>
    </AppSidebar>
  );
}
