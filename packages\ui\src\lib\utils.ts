import { clsx, type ClassValue } from "clsx";
import { twMerge } from "tailwind-merge";
import { AppliedFilters } from "../components/FilterContainer";
import { Category, TransactionCategory } from "../components/TransactionsTable";
import { Group, Member } from "../components/MemberGroupFilter";


export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs));
}

export function formatINRCurrency(number: number) {
  return new Intl.NumberFormat("en-IN", {
    style: "currency",
    currency: "INR",
    minimumFractionDigits: 0,
  }).format(number);
}

export function generateFilterBadgesFromFilters(
  filters: AppliedFilters,
  categories: Category[],
  actualMembers?: Member[],
  actualGroups?: Group[]  ) {
  const badges: string[] = [];

  if (
    filters.selectedDateLabel &&
    filters.selectedDateLabel !== "All time"
  ) {
    badges.push(filters.selectedDateLabel);
  }

  if (
    filters.selectedAmountRange &&
    filters?.selectedAmountRange?.length === 2 &&
    (filters.selectedAmountRange[0]! > 0 ||
      filters.selectedAmountRange[1]! < 200000)
  ) {
    const formattedMin = formatINRCurrency(filters.selectedAmountRange[0]!);
    const formattedMax = formatINRCurrency(filters.selectedAmountRange[1]!);
    badges.push(`${formattedMin} - ${formattedMax}`);  }

  const memberMap = new Map<string, string>();
  const groupMap = new Map<string, string>();

  if (Array.isArray(actualMembers) && actualMembers.length > 0) {
    actualMembers.forEach(member => {
      if (member && member.id) {
        memberMap.set(member.id, member.name);
      }
    });
  }

  if (Array.isArray(actualGroups) && actualGroups.length > 0) {
    actualGroups.forEach(group => {
      if (group && group.id) {
        groupMap.set(group.id, group.groupName);
      }
    });
  }

  if (filters.selectedMembers?.length > 0) {
    filters.selectedMembers.forEach(id => {
      const memberName = memberMap.get(id);
      if (memberName) {
        badges.push(memberName);
      }
    });
  }
  if (filters.selectedMembers?.length === 0 && filters.selectedGroups?.length > 0) {
    filters.selectedGroups.forEach(id => {
      const groupName = groupMap.get(id);
      if (groupName) {
        badges.push(groupName);
      }
    });
  }
  if (filters.selectedAccounts && filters.selectedAccounts.accountIds.size > 0) {
    const accountMemberMap = new Map<string, string>();
    const accountGroupMap = new Map<string, string>();


    filters.selectedAccounts.accountIds.forEach((accountId) => {
      if (filters.selectedMembers?.length > 0) {
        const memberName = accountMemberMap.get(accountId);
        if (memberName && !badges.includes(memberName)) {
          badges.push(memberName);
        }
      }
      else if (filters.selectedGroups?.length > 0) {
        const groupName = accountGroupMap.get(accountId);
        if (groupName && !badges.includes(groupName)) {
          badges.push(groupName);
        }
      }
    });
  }

  if (
    filters.selectedTransactionType &&
    filters.selectedTransactionType !== "All"
  ) {
    badges.push(filters.selectedTransactionType);
  }

  if (
    filters.selectedTagStatus &&
    filters.selectedTagStatus !== "All"
  ) {
    badges.push(filters.selectedTagStatus);
  }

  if (filters.selectedBookmarkOptions?.showFavorites) {
    badges.push("Favorites");
  }
  if (filters.selectedBookmarkOptions?.excludedFromCashflow) {
    badges.push("Excluded from cashflow");
  }
  if (filters.selectedBookmarkOptions?.withNotes) {
    badges.push("With notes");
  }

  if (filters.selectedCategories && categories && categories.length > 0) {
    Object.entries(filters.selectedCategories).forEach(
      ([categoryId, subcategoryIds]) => {
        const category = categories.find((c) => c.id === categoryId);
        if (!category) {
          badges.push(categoryId);
          return;
        }
        const categoryName = category.name;
        if (subcategoryIds.length > 0) {
          const hasOther = subcategoryIds.some(subId => {
            const subCategory = category.subCategories.find(s => s.id === subId);
            const subName = subCategory?.name || '';
            return subName.toLowerCase() === "general" || subName.toLowerCase() === "others";
          });

          if (hasOther) {
            if (!badges.includes(categoryName)) {
              badges.push(categoryName);
            }
            subcategoryIds.forEach(subId => {
              const subCategory = category.subCategories.find(s => s.id === subId);
              if (subCategory) {
                const subName = subCategory.name;
                if (!(hasOther && (subName.toLowerCase() === "general" || subName.toLowerCase() === "others"))) {
                  badges.push(subName);
                }
              }
            });
          } else {
            subcategoryIds.forEach(subId => {
              const subCategory = category.subCategories.find(s => s.id === subId);
              if (subCategory) {
                badges.push(subCategory.name);
              }
            });
          }
        } else if (categoryName) {
          badges.push(categoryName);
        }
      }
    );
  } else if (filters.selectedCategories) {
    Object.keys(filters.selectedCategories).forEach(categoryId => {
      badges.push(categoryId);
    });
  }
  return badges;
}

// API interface (snake_case as received from the backend)
export interface ApiAccountAPI {
  account_id: string;
  linked_acc_ref: string;
  masked_acc_number: string;
  fip_id: string;
  holder_name: string;
  branch: string;
  fi_type: string;
  user_id: string;
  data_synced_at: string;
}

// Frontend interface (camelCase for use in the UI)
export interface ApiAccount {
  accountId: string;
  linkedAccRef: string;
  maskedAccNumber: string;
  fipId: string;
  holderName: string;
  branch: string;
  fiType: string;
  userId: string;
  dataSyncedAt: string;
}

// For backward compatibility
export type ApiAccountSnakeCase = ApiAccountAPI;
export type ApiAccountCamel = ApiAccount;

/**
 * Formats an account object from the API
 * @param account The account object from the API (snake_case or camelCase)
 * @returns The formatted account object (in the same format as the input)
 */
export function formatApiAccount(account: ApiAccountAPI | ApiAccount): any {
  if ('accountId' in account) {
    // Handle camelCase account
    return {
      ...account,
      branch: account.branch || "Bank Account",
      maskedAccNumber: account.maskedAccNumber,
      dataSyncedAt: account.dataSyncedAt,
    };
  } else {
    // Handle snake_case account
    return {
      ...account,
      branch: account.branch || "Bank Account",
      masked_acc_number: account.masked_acc_number,
      data_synced_at: account.data_synced_at,
    };
  }
}

export function getListFromParams(
  params: URLSearchParams,
  countKey: string,
  itemKeyPrefix: string
): string[] {
  const count = parseInt(params.get(countKey) || "0", 10);
  return Array.from({ length: count }, (_, i) => params.get(`${itemKeyPrefix}_${i}`))
    .filter((id): id is string => Boolean(id));
}

export function extractStringParams(
  params: Record<string, string | undefined>,
  prefix: string,
  count: number
): string[] {
  return Array.from({ length: count }, (_, i) => params[`${prefix}_${i}`])
    .filter((id): id is string => Boolean(id));
}

export function extractCategoryFilters(
  params: Record<string, string | undefined>,
  count: number
): TransactionCategory[] {
  return Array.from({ length: count }, (_, i) => {
    const categoryId = params[`categoryId_${i}`];
    const subcategoryId = params[`subcategoryId_${i}`] || "";
    return categoryId
      ? { categoryCollection: "", categoryId, subcategoryId }
      : null;
  })
    .filter((f): f is TransactionCategory => f !== null);
}
