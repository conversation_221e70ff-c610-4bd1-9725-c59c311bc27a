import React from "react";
import { cn } from "../lib/utils";

interface FilterProps {
  label: string;
  options: string[];
  selected: string;
  onSelect: (value: string) => void;
}

export const MoreFilter = ({
  label,
  options,
  selected,
  onSelect,
}: FilterProps) => {
  return (
    <div>
      <div className="flex flex-col gap-2 mb-6">
        <p className="text-sm font-medium">{label}</p>
        <div className="flex items-center">
          {options.map((option, index) => (
            <React.Fragment key={option}>
              {index > 0 && (
                <div
                  className="h-4 w-px bg-[#E0E0E0] mx-3"
                  aria-hidden="true"
                ></div>
              )}
              <button
                className={cn(
                  "text-sm px-9 py-1.5 rounded-md ",
                  selected === option
                    ? "bg-[#F4EEF9] text-[#6B2BA8]"
                    : "bg-white text-[#797878]"
                )}
                onClick={() => onSelect(option)}
              >
                {option}
              </button>
            </React.Fragment>
          ))}
        </div>
      </div>
    </div>
  );
};
