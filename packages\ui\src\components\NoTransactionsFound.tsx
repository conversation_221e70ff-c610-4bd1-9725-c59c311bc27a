interface NoTransactionFoundSvgProps {
  className?: string;
}
export const NoTransactionFoundSvg: React.FC<NoTransactionFoundSvgProps> = ({
  className,
}) => {
  return (
    <svg
      width="196"
      height="171"
      viewBox="0 0 196 171"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      className={className}
    >
      <g clipPath="url(#clip0_3104_24697)">
        <g clipPath="url(#clip1_3104_24697)">
          <path
            d="M35.0994 58.6445H78.9258V137.757H35.0994V58.6445Z"
            fill="#C4C4C4"
          />
        </g>
        <path
          d="M89.4939 3.50391H133.32V138.941H89.4939V3.50391Z"
          fill="#C4C4C4"
        />
        <path
          d="M143.736 95.6562H187.562V138.934H143.736V95.6562Z"
          fill="#C4C4C4"
        />
        <path
          d="M113.229 25.543H153.995C159.35 25.543 163.688 29.879 163.688 35.2319V58.7213C163.688 64.0742 159.35 68.4102 153.995 68.4102H146.631L134.036 78.511L122.128 68.4102H113.235C107.88 68.4102 103.543 64.0742 103.543 58.7213V35.2319C103.543 29.879 107.88 25.543 113.235 25.543H113.229Z"
          fill="#1E1E1E"
        />
        <path
          d="M134.039 79.0473C134.157 79.0473 134.276 79.0099 134.369 78.9288L146.82 68.9404H153.997C159.639 68.9404 164.227 64.3548 164.227 58.7148V35.2255C164.227 29.5856 159.639 25 153.997 25H113.231C107.589 25 103.002 29.5856 103.002 35.2255V58.7148C103.002 64.3548 107.589 68.9404 113.231 68.9404H121.931L133.689 78.9163C133.789 79.0036 133.914 79.0411 134.032 79.0411L134.039 79.0473ZM153.997 26.0731C159.047 26.0731 163.159 30.1845 163.159 35.2317V58.7211C163.159 63.7683 159.047 67.8797 153.997 67.8797H146.633C146.514 67.8797 146.396 67.9234 146.302 67.9983L134.051 77.8245L122.474 68.0045C122.38 67.9234 122.256 67.8797 122.131 67.8797H113.237C108.188 67.8797 104.075 63.7683 104.075 58.7211V35.2317C104.075 30.1845 108.188 26.0731 113.237 26.0731H154.004H153.997Z"
          fill="#1E1E1E"
        />
        <path
          d="M142 39L124 57"
          stroke="white"
          strokeWidth="4"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
        <path
          d="M124 39L142 57"
          stroke="white"
          strokeWidth="4"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
        <path
          d="M78.2178 138.449C80.9531 140.041 84.4663 139.112 86.0584 136.376L110.525 94.2918C112.117 91.5561 111.188 88.0425 108.453 86.4502C105.717 84.8579 102.204 85.7871 100.612 88.5228L76.1454 130.607C74.5533 133.343 75.4824 136.857 78.2178 138.449Z"
          fill="#1E1E1E"
        />
        <path
          d="M109.89 98.337L97.4129 91.0801C96.4336 90.5106 96.0968 89.2517 96.6689 88.2678L103.613 76.3262C104.182 75.3469 105.441 75.01 106.425 75.5822L118.902 82.8391C119.881 83.4086 120.218 84.6675 119.646 85.6513L112.702 97.5929C112.132 98.5723 110.874 98.9092 109.89 98.337Z"
          fill="white"
        />
        <path
          d="M110.629 98.8498C110.055 98.8498 109.476 98.7036 108.948 98.3956L97.8294 91.9271C97.0569 91.4781 96.5035 90.7524 96.2738 89.8858C96.0442 89.0191 96.1642 88.1159 96.6184 87.338L102.773 76.7504C103.702 75.1476 105.764 74.6046 107.361 75.5339L118.48 82.0024C120.083 82.9317 120.626 84.9939 119.697 86.5915L113.542 97.1792C112.921 98.2494 111.793 98.8498 110.634 98.8498H110.629ZM105.675 76.1134C104.877 76.1134 104.094 76.5259 103.66 77.2672L97.5058 87.8549C97.1926 88.3926 97.1091 89.0191 97.2657 89.6195C97.4223 90.2199 97.8086 90.7211 98.3462 91.0343L109.465 97.5029C110.572 98.145 111.997 97.7691 112.644 96.6623L118.799 86.0746C119.441 84.9678 119.065 83.5426 117.958 82.8952L106.839 76.4267C106.474 76.2126 106.072 76.1134 105.675 76.1134Z"
          fill="#1E1E1E"
        />
        <path
          d="M133.757 0C109.056 0 89.0312 20.0268 89.0312 44.7314C89.0312 69.436 109.056 89.4628 133.757 89.4628C158.459 89.4628 178.483 69.436 178.483 44.7314C178.483 20.0268 158.459 0 133.757 0ZM133.757 84.8268C111.619 84.8268 93.6667 66.8779 93.6667 44.7314C93.6667 22.585 111.619 4.64125 133.757 4.64125C155.896 4.64125 173.848 22.5902 173.848 44.7366C173.848 66.8831 155.901 84.832 133.757 84.832V84.8268Z"
          fill="#1E1E1E"
        />
        <path
          d="M141.094 19.6091C141.011 19.6091 140.922 19.5882 140.843 19.5465C140.593 19.4055 140.504 19.0923 140.645 18.8417L144.779 11.4021C144.92 11.1515 145.234 11.0627 145.484 11.2037C145.735 11.3447 145.823 11.6579 145.683 11.9085L141.548 19.3481C141.454 19.5204 141.277 19.6143 141.094 19.6143V19.6091Z"
          fill="#1E1E1E"
        />
        <path
          d="M126.215 19.6104C126.042 19.6104 125.87 19.5216 125.776 19.365L121.851 12.9591C121.699 12.7138 121.778 12.3953 122.023 12.2491C122.268 12.0977 122.587 12.176 122.733 12.4214L126.658 18.8273C126.81 19.0727 126.731 19.3911 126.486 19.5373C126.403 19.5895 126.309 19.6156 126.215 19.6156V19.6104Z"
          fill="#1E1E1E"
        />
        <path
          d="M116.454 117.263C116.454 117.263 126.889 128.415 119.09 137.29C114.199 142.86 101.399 137.89 101.399 137.89L101.133 121.22L116.454 117.258V117.263Z"
          fill="white"
        />
        <path
          d="M111.762 140.454C110.702 140.454 109.559 140.365 108.338 140.193C104.511 139.645 101.343 138.428 101.212 138.376C101.014 138.298 100.883 138.115 100.883 137.901L100.617 121.231C100.617 120.991 100.774 120.782 101.003 120.725L116.325 116.762C116.507 116.715 116.7 116.773 116.831 116.908C116.941 117.023 119.519 119.811 121.174 123.748C123.393 129.041 122.808 133.845 119.477 137.63C117.828 139.509 115.239 140.454 111.757 140.454H111.762ZM101.912 137.525C103.906 138.235 114.529 141.702 118.705 136.946C121.79 133.432 122.307 129.13 120.234 124.171C118.919 121.017 116.92 118.574 116.283 117.838L101.661 121.618L101.917 137.525H101.912Z"
          fill="#1E1E1E"
        />
        <path
          d="M37.0458 76.8972L57.7801 88.2993C60.1605 89.6097 62.8384 90.2936 65.5528 90.2518C70.2666 90.1788 80.5868 92.4654 97.8602 102.082C141.84 126.573 145.259 130.723 141.741 137.662C138.948 143.175 129.478 142.689 110.206 131.485C110.206 131.485 116.371 144.24 103.42 148.207C103.42 148.207 103.994 160.701 92.6296 161.066C92.6296 161.066 93.8877 163.88 90.385 167.963C86.4803 172.52 73.3517 174.306 21.5629 142.277L6.98828 133.929L37.0458 76.8867V76.8972Z"
          fill="white"
        />
        <path
          d="M83.4527 170.994C80.8061 170.994 77.5644 170.441 73.785 169.334C61.9145 165.873 44.2548 156.919 21.2967 142.724L6.7273 134.381C6.48717 134.245 6.39843 133.937 6.52894 133.692L36.5864 76.65C36.649 76.5299 36.7639 76.4359 36.8944 76.3941C37.0249 76.3524 37.1711 76.368 37.2911 76.4359L58.0307 87.838C60.3536 89.1119 62.948 89.7697 65.5424 89.7279C68.8207 89.6757 78.551 90.7355 98.1108 101.626C121.064 114.406 132.235 121.256 137.94 126.054C143.99 131.134 144.1 134.136 142.195 137.895C141.359 139.539 139.992 140.656 138.128 141.21C137.22 141.481 136.192 141.612 135.048 141.612C129.651 141.612 121.664 138.62 111.229 132.674C111.955 134.767 112.925 138.829 111.307 142.536C110.081 145.35 107.601 147.381 103.931 148.582C103.931 150.012 103.67 154.883 100.538 158.271C98.732 160.224 96.2942 161.325 93.2874 161.55C93.491 162.693 93.4649 165.163 90.7713 168.3C89.5393 169.736 87.7071 170.608 85.3215 170.89C84.7368 170.958 84.1104 170.994 83.4475 170.994H83.4527ZM7.67736 133.734L21.8187 141.831C44.7141 155.99 62.2903 164.907 74.0721 168.342C82.1946 170.712 87.5505 170.472 89.9883 167.627C93.2039 163.878 92.1964 161.378 92.1494 161.273C92.0815 161.117 92.0972 160.934 92.186 160.793C92.2747 160.652 92.4313 160.558 92.6036 160.553C95.626 160.453 98.0377 159.456 99.776 157.577C103.112 153.975 102.898 148.289 102.892 148.232C102.882 147.997 103.033 147.783 103.258 147.715C106.823 146.624 109.214 144.744 110.357 142.129C112.424 137.404 109.757 131.771 109.731 131.713C109.632 131.51 109.679 131.264 109.846 131.108C110.013 130.951 110.258 130.925 110.456 131.04C117.211 134.966 123.021 137.717 127.719 139.215C132.026 140.589 135.429 140.928 137.831 140.218C139.412 139.748 140.566 138.813 141.271 137.43C142.236 135.524 142.461 134.172 142.049 132.768C141.542 131.045 140.023 129.166 137.272 126.853C131.619 122.102 120.495 115.284 97.6044 102.54C89.8682 98.2325 82.8785 95.0322 76.8283 93.0275C71.034 91.1062 67.4948 90.7408 65.5529 90.7721C62.781 90.8139 60.0039 90.1195 57.5243 88.7569L37.2494 77.6053L7.67214 133.739L7.67736 133.734Z"
          fill="#1E1E1E"
        />
        <path
          d="M110.207 132.007C110.123 132.007 110.04 131.986 109.961 131.944L93.0952 122.896C92.8446 122.761 92.7507 122.447 92.8864 122.197C93.0221 121.946 93.3353 121.852 93.5859 121.988L110.452 131.036C110.703 131.171 110.797 131.485 110.661 131.735C110.567 131.907 110.389 132.007 110.207 132.007Z"
          fill="#1E1E1E"
        />
        <path
          d="M103.414 148.722C103.336 148.722 103.263 148.707 103.19 148.67L85.0496 139.79C84.7938 139.664 84.6894 139.356 84.8147 139.1C84.94 138.845 85.248 138.74 85.5037 138.865L103.644 147.746C103.899 147.871 104.004 148.179 103.879 148.435C103.79 148.618 103.607 148.722 103.414 148.722Z"
          fill="#1E1E1E"
        />
        <path
          d="M37.2458 69.0497L0 139.137L22.8863 151.302L60.132 81.2149L37.2458 69.0497Z"
          fill="#1E1E1E"
        />
        <path
          d="M46.5092 79.0303C50.0537 80.8576 51.4474 85.2117 49.6204 88.7566C47.7933 92.3014 43.4398 93.6954 39.8953 91.8681C36.3508 90.0409 34.9571 85.6868 36.7841 82.1419C38.6111 78.597 42.9647 77.203 46.5092 79.0303Z"
          fill="#1E1E1E"
        />
        <path
          d="M176.893 89.1715C174.951 89.1715 172.377 88.7852 170.707 87.0362C170.012 87.6575 168.974 88.4563 167.7 88.6651C166.499 88.8635 164.959 88.4041 164.338 87.1459C164.213 86.8901 164.317 86.582 164.573 86.4515C164.829 86.3262 165.137 86.4306 165.267 86.6865C165.664 87.4852 166.729 87.7776 167.533 87.6419C168.332 87.5113 169.135 87.0571 170.07 86.2166C169.224 84.9062 168.89 83.152 169.041 80.8653C169.167 78.9806 170.012 76.7357 171.986 76.6208C173.484 76.5373 174.632 77.8999 174.841 79.2103C175.065 80.6252 174.465 81.9669 173.99 82.8492C173.312 84.1231 172.461 85.2821 171.458 86.3106C173.212 88.1483 176.271 88.2161 178.02 88.0804C179.403 87.9708 181.533 87.6262 183.24 86.5455C182.442 85.6214 181.982 84.4676 182.008 83.3191C182.029 82.4211 182.442 81.5179 183.084 80.9541C183.569 80.5312 184.138 80.3485 184.728 80.4163C185.678 80.5312 186.508 81.377 186.696 82.4211C186.842 83.2251 186.649 84.1283 186.137 85.021C185.782 85.6371 185.344 86.1644 184.848 86.6134C184.937 86.676 185.025 86.7334 185.114 86.7857C186.263 87.4696 187.782 87.7202 189.389 87.4905C189.671 87.4487 189.932 87.6471 189.974 87.929C190.016 88.2109 189.818 88.472 189.536 88.5137C187.698 88.7748 185.939 88.4772 184.587 87.6784C184.383 87.5583 184.185 87.4226 184.002 87.2764C182.071 88.5764 179.649 88.9836 178.093 89.1089C177.738 89.135 177.331 89.1559 176.893 89.1559V89.1715ZM184.477 81.4553C184.227 81.4553 183.981 81.5545 183.752 81.7528C183.334 82.1183 183.042 82.7605 183.031 83.3556C183.011 84.2745 183.397 85.2038 184.065 85.9451C184.519 85.5483 184.921 85.0785 185.234 84.5251C185.506 84.05 185.803 83.3347 185.673 82.6247C185.563 82.0243 185.099 81.5231 184.597 81.4605C184.556 81.4605 184.519 81.4553 184.477 81.4553ZM172.106 77.6598C172.106 77.6598 172.059 77.6598 172.033 77.6598C170.644 77.7381 170.143 79.7585 170.065 80.9384C169.934 82.8858 170.185 84.3789 170.822 85.4857C171.709 84.5564 172.461 83.5122 173.071 82.3741C173.708 81.1786 173.948 80.2232 173.813 79.3826C173.677 78.5316 172.967 77.6598 172.106 77.6598Z"
          fill="#1E1E1E"
        />
        <path
          d="M92.6318 161.587C92.5691 161.587 92.5013 161.577 92.4386 161.551L75.1809 154.487C74.9147 154.378 74.7894 154.08 74.899 153.814C75.0087 153.548 75.3114 153.422 75.5724 153.532L92.8302 160.596C93.0964 160.705 93.2217 161.003 93.112 161.269C93.0285 161.467 92.8354 161.587 92.6318 161.587Z"
          fill="#1E1E1E"
        />
      </g>
      <defs>
        <clipPath id="clip0_3104_24697">
          <rect width="195.468" height="170.995" fill="white" />
        </clipPath>
        <clipPath id="clip1_3104_24697">
          <rect
            width="44"
            height="79"
            fill="white"
            transform="translate(35 60)"
          />
        </clipPath>
      </defs>
    </svg>
  );
};

export function NoTransactionsFound() {
  return (
    <div className="flex flex-col gap-12 w-fit">
      <span className="text-sm text-center font-medium">No transactions found</span>
      <NoTransactionFoundSvg className="max-w-full" />
    </div>
  );
};
