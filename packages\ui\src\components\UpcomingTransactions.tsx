import { format } from "date-fns";
import { CheckSquare, SaveCircle, X,  } from "../icons";
import { RecurrentTransactionCard } from "./RecurrentTransactionsTable";
import { calculateTotal, formatAmountDisplay, formatTotalAmount } from "./RecurrentTransactionDetails";

export interface TransactionSectionProps {
    transactions: RecurrentTransactionCard[];
    editMode: boolean;
    onToggleEditMode: () => void;
    onCompleteTransaction?: (txnId: string) => void;
    onDeleteTransaction?: (txnId: string) => void;
  }

  
  
  export function UpcomingTransaction({
    transactions,
    editMode,
    onToggleEditMode,
    onCompleteTransaction
  }: TransactionSectionProps) {
    if (transactions.length === 0) {
      return null;
    }
  
    return (
      <div className="p-4 rounded-2xl border border-[#F4EEF9] pl-[29px] flex flex-col gap-3 bg-white">
        <div className="flex justify-between">
          <div className="flex items-center gap-2 text-primary font-semibold">
            <span className="text-base font-medium">Upcoming transactions</span>
          </div>
          <div onClick={onToggleEditMode} className="cursor-pointer">
            {editMode ? (
              <span className="text-primary text-xl">
                <X width={24} height={24} />
              </span>
            ) : (
              <CheckSquare width={24} height={24} />
            )}
          </div>
        </div>
  
        <div className="text-xs">
          <span className="text-[#6B6B6B] mr-1">Total</span>
          <span>{formatTotalAmount(calculateTotal(transactions))}</span>
          <span> for {transactions.length} transactions</span>
        </div>
          {transactions.map((txn, index) => (
          <div
            key={index}
            className="flex justify-between items-center text-sm py-2 border-b last:border-none border-dashed border-gray-200"
          >
            <div className="text-[#1E1E1E] font-medium">
              {format(
                new Date(Number(txn.txnCard.txnTimestamp) * 1000),
                "do MMM yyyy"
              )}
            </div>
            <div className="flex items-center gap-2">
              <div>{formatAmountDisplay(txn.txnCard.amount)}</div>
              {editMode && onCompleteTransaction && (
                <div
                  className="text-green-500 cursor-pointer ml-2"
                  onClick={() => onCompleteTransaction(txn.txnCard.txnId)}
                >
                  <SaveCircle width={24} height={24} />
                </div>
              )}
            </div>
          </div>
        ))}
      </div>
    );
  }