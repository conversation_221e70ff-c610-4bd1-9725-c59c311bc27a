# End-to-End Testing Framework

This package contains a comprehensive end-to-end testing framework built using Playwright. The framework follows the Page Object Model pattern and is designed to be modular, maintainable, and extensible.

## Table of Contents

1. [Overview](#overview)
2. [Directory Structure](#directory-structure)
3. [Running Tests](#running-tests)
4. [Test Types](#test-types)
5. [Writing Tests](#writing-tests)
6. [Page Object Model](#page-object-model)
7. [Test Fixtures](#test-fixtures)
8. [Best Practices](#best-practices)
9. [Troubleshooting](#troubleshooting)
10. [Extending the Framework](#extending-the-framework)

## Overview

The e2e testing framework is designed to verify the functionality of web components using Playwright. The framework is optimized for fast execution and reliable results, with proper error handling and reporting.

The framework supports multiple test types:

1. **Page Tests**: Test full pages and user flows
2. **Component Tests**: Test individual UI components via Storybook
3. **Visual Tests**: Verify visual appearance with screenshot comparison
4. **Accessibility Tests**: Ensure components meet accessibility standards
5. **API Tests**: Test API endpoints directly

## Directory Structure

```
e2e-tests/
├── tests/
│   ├── fixtures/                   # Test fixtures and setup
│   │   ├── test-fixtures.ts        # Page test fixtures
│   ├── models/                     # Page Object Models
│   │   ├── BasePage.ts             # Base class for all page objects
│   │   └── [Component]Page.ts      # Component-specific page objects
│   ├── specs/                      # Test specifications
│   │   ├── pages/                  # Page tests
│   │   │   └── [page].spec.ts      # Page functionality tests
│   │   ├── visual/                 # Visual tests
│   │   │   └── [component].visual.spec.ts # Visual regression tests
│   │   ├── accessibility/          # Accessibility tests
│   │   │   └── [component].a11y.spec.ts   # Accessibility tests
│   │   └── api/                    # API tests
│   │       └── [endpoint].api.spec.ts     # API endpoint tests
│   └── utils/                      # Utility functions
│       ├── logger.ts               # Logging utilities
│       ├── component-test-helper.ts # Component testing helpers
│       ├── visual-comparison.ts    # Visual testing utilities
│       └── accessibility.ts        # Accessibility testing utilities
├── playwright.config.ts            # Playwright configuration
└── package.json                    # Package configuration
```

## Running Tests

### Prerequisites

- Node.js 18 or higher
- pnpm 9 or higher

### Configuration

The test framework can be configured using the following environment variables:

- `BASE_URL`: The base URL for all tests 
- `CI`: When set, modifies test behavior for CI environments:
  - Increases retry attempts to 2 (vs 1 in development)
  - Disables parallel test execution
  - Enforces stricter test rules (e.g., no test.only)

### Installation

```bash
# Install dependencies
pnpm install

# Install Playwright browsers (only Chromium for faster installation)
pnpm install:browsers
```

### Running Tests

```bash
# Run all tests
pnpm test:e2e

# Run specific test types
pnpm test:pages         # Run page tests
pnpm test:components    # Run component tests
pnpm test:visual        # Run visual tests
pnpm test:accessibility # Run accessibility tests
pnpm test:api           # Run API tests

# Run tests with UI mode
pnpm test:ui

# Run tests in headed mode
pnpm test:headed

# Run tests in debug mode
pnpm test:debug

# Show test report
pnpm test:report

# Run tests in CI environment
pnpm test:ci
```

## Test Types

### Page Tests

Page tests verify the functionality of full pages and user flows. They use the Page Object Model pattern to encapsulate page structure and behavior.

```typescript
import { test, expect } from '../../fixtures/test-fixtures';

test.describe('Login Page Tests', () => {
  test('should display the login page correctly', async ({ loginPage }) => {
    // Check that elements are visible
    expect(await loginPage.heading.isVisible()).toBe(true);
    expect(await loginPage.phoneInput.isVisible()).toBe(true);
  });
});
```

### Component Tests

Component tests verify the functionality of individual UI components via Storybook. They use the component test helper to set up the test environment.

```typescript
import { test, expect } from '../../fixtures/storybook-fixtures';
import { runComponentTest } from '../../utils/component-test-helper';

test.describe('Button Component Tests', () => {
  test('should display the default button correctly', async ({ storybookPage }) => {
    await runComponentTest(
      {
        storyId: 'components-button--default',
        componentSelector: 'button',
        screenshotBaseName: 'button-default',
      },
      storybookPage,
      async ({ component }) => {
        // Check that the button is visible
        await expect(component).toBeVisible();
      }
    );
  });
});
```

### Visual Tests

Visual tests verify the visual appearance of components by comparing screenshots with baselines.

```typescript
import { test, expect } from '../../fixtures/test-fixtures';
import { expectScreenshotMatch } from '../../utils/visual-comparison';

test.describe('Login Page Visual Tests', () => {
  test('should match the login page visual baseline', async ({ loginPage, page }) => {
    await expectScreenshotMatch(
      page,
      'login-page',
      { threshold: 0.1 }
    );
  });
});
```

### Accessibility Tests

Accessibility tests ensure components meet accessibility standards using the axe-core library.

```typescript
import { test, expect } from '../../fixtures/test-fixtures';
import { AxeBuilder } from '@axe-core/playwright';

test.describe('Login Page Accessibility Tests', () => {
  test('should not have any automatically detectable accessibility issues', async ({ loginPage, page }) => {
    const accessibilityScanResults = await new AxeBuilder({ page })
      .withTags(['wcag2a', 'wcag2aa'])
      .analyze();

    expect(accessibilityScanResults.violations).toEqual([]);
  });
});
```

### API Tests

API tests verify the functionality of API endpoints directly.

```typescript
import { test, expect } from '@playwright/test';

test.describe('Authentication API Tests', () => {
  test('should return 401 for unauthenticated requests', async ({ request }) => {
    const response = await request.get('/api/protected');
    expect(response.status()).toBe(401);
  });
});
```

## Writing Tests

Tests should be organized in component-specific directories and follow this pattern:

```typescript
import { test, expect } from '../../fixtures/test-fixtures';

test.describe('Component Tests', () => {
  test('should display the component correctly', async ({ componentPage }) => {
    // Check that elements are visible
    expect(await componentPage.heading.isVisible()).toBe(true);
    expect(await componentPage.button.isVisible()).toBe(true);
  });

  test('should interact with the component', async ({ componentPage, page }) => {
    // Interact with the component
    const result = await componentPage.performAction();

    // Verify the result
    expect(result.success).toBe(true);
  });
});
```

## Page Object Model

The framework uses the Page Object Model (POM) pattern to encapsulate page structure and behavior. Each page or component should have its own page object class that extends the `BasePage` class:

```typescript
import { Page, Locator } from '@playwright/test';
import { BasePage } from './BasePage';

interface ActionResult {
  success: boolean;
  message: string;
}

export class ComponentPage extends BasePage {
  // Locators
  readonly heading: Locator;
  readonly button: Locator;

  constructor(page: Page) {
    super(page, '/component-path');

    // Initialize locators
    this.heading = page.locator('h1');
    this.button = page.getByRole('button', { name: 'Action' });
  }

  // Component-specific methods
  async performAction(): Promise<ActionResult> {
    await this.click(this.button);

    // Return result
    return {
      success: true,
      message: 'Action performed successfully'
    };
  }
}
```

## Test Fixtures

Test fixtures are used to set up the test environment and provide access to page objects:

```typescript
import { test as base, expect } from '@playwright/test';
import { ComponentPage } from '../models/ComponentPage';

/**
 * Custom fixtures for our tests
 */
type CustomFixtures = {
  componentPage: ComponentPage;
};

/**
 * Extended test with custom fixtures
 */
export const test = base.extend<CustomFixtures>({
  // Component page fixture
  componentPage: async ({ page }, use) => {
    // Create component page object
    const componentPage = new ComponentPage(page);

    // Navigate to the component page
    await componentPage.goto();

    // Wait for page to load
    await componentPage.waitForPageLoad();

    // Use the fixture
    await use(componentPage);
  },
});

// Export expect for convenience
export { expect };
```

## Best Practices

1. **Use Page Object Model**: Encapsulate page structure and behavior in page objects to make tests more maintainable.
2. **Write Focused Tests**: Each test should test one specific aspect of a component.
3. **Use Descriptive Test Names**: Test names should clearly describe what the test is verifying.
4. **Use Test Fixtures**: Use test fixtures to set up the test environment and provide access to page objects.
5. **Handle Asynchronous Operations**: Use `async/await` to handle asynchronous operations in tests.
6. **Error Handling**: Implement proper error handling in page objects to make tests more robust.
7. **Logging**: Use logging to provide visibility into test execution and help with debugging.
8. **Test Isolation**: Ensure each test is independent and doesn't rely on the state from previous tests.
9. **Optimize Test Speed**: Keep tests fast by minimizing unnecessary waits and operations.
10. **Maintain Test Data**: Keep test data in a central location for easy updates.

## Troubleshooting

### Common Issues

1. **Tests failing due to timing issues**
   - Increase timeouts in the page object methods
   - Add explicit waits for elements to be visible or enabled

2. **Elements not found**
   - Check the locators in the page object
   - Use more robust locators (e.g., test IDs instead of CSS selectors)

3. **Redirects not happening**
   - Design tests to handle conditional flows
   - Use result objects to indicate success even when redirects don't occur

### Debugging Tips

1. Use `test:debug` to run tests in debug mode
2. Check the test report for screenshots and videos
3. Add more logging to the page object methods
4. Use `page.pause()` to pause test execution and inspect the page

## Extending the Framework

To extend the framework to test other components:

1. Create new page object classes in `tests/models/`
2. Add new fixtures in `tests/fixtures/test-fixtures.ts`
3. Create new test files in `tests/specs/`
4. Update the playwright.config.ts file to include the new test files

For example, to add tests for a new component:

1. Create a `ComponentPage.ts` file in `tests/models/`
2. Add a `componentPage` fixture in `tests/fixtures/test-fixtures.ts`
3. Create a `component.spec.ts` file in `tests/specs/component/`
4. Update the `testMatch` pattern in playwright.config.ts if needed
