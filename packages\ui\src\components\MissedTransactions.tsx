import { format } from "date-fns";
import { CheckSquare, SaveCircle, X } from "../icons";
import { TransactionSectionProps } from "./UpcomingTransactions";
import { formatAmountDisplay } from "./RecurrentTransactionDetails";

export function MissedTransaction({
  transactions,
  editMode,
  onToggleEditMode,
  onCompleteTransaction
}: TransactionSectionProps) {
  const hasTransactions = transactions.length > 0;

  return (
    <div className="p-4 rounded-2xl border border-[#F4EEF9] flex flex-col gap-3 pl-[29px] bg-white">
      <div className="flex justify-between text-sm">
        <div className="flex items-center gap-2 text-primary font-semibold">
          <span className="text-base font-medium">Missed payment</span>
        </div>
        <div onClick={onToggleEditMode} className="cursor-pointer">
          {editMode ? (
            <span className="text-primary text-xl">
              <X width={24} height={24} />
            </span>
          ) : (
            <>
              {hasTransactions && (
                <CheckSquare width={24} height={24} />
              )}
            </>
          )}
        </div>
      </div>

      {!hasTransactions ? (
        <div className="text-sm text-[#C4C4C4] font-medium pt-5">
          No transactions found
        </div>
      ) : (
        transactions.map((txn, index) => (
          <div
            key={index}
            className="flex justify-between items-center text-sm py-2 border-b last:border-none border-dashed border-gray-200"
          >
            <div className="text-[#1E1E1E] font-medium">
              {format(
                new Date(Number(txn.txnCard.txnTimestamp) * 1000),
                "do MMM yyyy"
              )}
            </div>
            <div className="flex items-center gap-2">
              <div>{formatAmountDisplay(txn.txnCard.amount)}</div>
              {editMode && onCompleteTransaction && (
                <div
                  className="text-green-500 cursor-pointer ml-2"
                  onClick={() => onCompleteTransaction(txn.txnCard.txnId)}
                >
                  <SaveCircle width={24} height={24} />
                </div>
              )}
            </div>
          </div>
        ))
      )}
    </div>
  );
}