import { FetchDocuments } from "@repo/ui";
import { requestMetadata, createGrpcClient } from "./utils/create-grpc-client";
import { GRPC_CONFIG } from "./constants";
import {
  DepositTransactionClient,
  GetTransactionResponse,
  Transaction,
  TransactionFilters,
} from "./types/transaction.types";
import { applyTransactionFilters } from "./utils/transaction-filters";

export enum SearchBy {
  SEARCH_BY_ALL = 0,
  SEARCH_BY_TAG = 1,
  SEARCH_BY_MERCHANT = 2,
  SEARCH_BY_USER = 3,
  SEARCH_BY_NOTES = 4,
  SEARCH_BY_AMOUNT = 5,
  SEARCH_BY_REMARKS = 6,
}

const transactionClient: DepositTransactionClient = createGrpcClient<DepositTransactionClient>({
  protoPath: GRPC_CONFIG.PROTO_FILES.TRANSACTION,
  servicePath: 'backend_services.visualization',
  serviceConstructor: 'DepositTransaction'
});

export function getTransactions(
  authToken: string,
  pageSize: number,
  pageNumber: number,
  filters?: TransactionFilters
): Promise<GetTransactionResponse> {
  requestMetadata.set("authorization", authToken);
  const request = applyTransactionFilters(filters || {});
  request.pagination_params = {
    page_size: pageSize,
    page_number: pageNumber,
  };

  return new Promise((resolve, reject) => {
    transactionClient.FetchDepositTxns(
      request,
      requestMetadata,
      (error, response) => {
        if (error) {
          console.error("FetchDepositTxns error:", error);
          reject(error);
        } else {
          resolve(response);
        }
      },
    );
  });
}

export function saveTransaction(
  authToken: string,
  transaction_id: string,
  favorite: boolean,
): Promise<object> {
  requestMetadata.set("authorization", authToken);
  return new Promise((resolve, reject) => {
    transactionClient.MarkDepositTxnFavorite(
      {
        transaction_id,
        favorite,
      },
      requestMetadata,
      (error, response) => {
        if (error) {
          reject(error);
        } else {
          resolve(response);
        }
      },
    );
  });
}

export function assignCategoryToTransaction(
  authToken: string,
  transaction_id: string,
  category_id: string,
  subcategory_id: string,
  collection: string = "",
): Promise<object> {
  requestMetadata.set("authorization", authToken);

  return new Promise((resolve, reject) => {
    transactionClient.AssignCategoryToDepositTxns(
      {
        transaction_ids: [transaction_id],
        category_id,
        subcategory_id,
        collection,
      },
      requestMetadata,
      (error, response) => {
        if (error) {
          reject(error);
        } else {
          resolve(response);
        }
      },
    );
  });
}

export function excludeTransactionFromCashFlow(
  authToken: string,
  transaction_id: string,
  flag: boolean,
): Promise<object> {
  requestMetadata.set("authorization", authToken);

  return new Promise((resolve, reject) => {
    transactionClient.ExcludeTxnFromCashFlow(
      {
        txn_id: transaction_id,
        exclude_cash_flow: flag,
      },
      requestMetadata,
      (error, response) => {
        if (error) {
          reject(error);
        } else {
          resolve(response);
        }
      },
    );
  });
}

export function updateTransactionNotes(
  authToken: string,
  transaction_id: string,
  notes: string,
): Promise<object> {
  requestMetadata.set("authorization", authToken);

  return new Promise((resolve, reject) => {
    transactionClient.UpdateTransactionNotes(
      {
        transaction_id,
        notes,
      },
      requestMetadata,
      (error, response) => {
        if (error) {
          reject(error);
        } else {
          resolve(response);
        }
      },
    );
  });
}

export function fetchTransactionDocumentsRPC(
  authToken: string,
  transaction_id: string,
): Promise<FetchDocuments> {
  requestMetadata.set("authorization", authToken);

  return new Promise((resolve, reject) => {
    transactionClient.FetchTransactionDocuments(
      {
        transaction_id,
      },
      requestMetadata,
      (error, response) => {
        if (error) {
          reject(error);
        } else {
          resolve(response);
        }
      },
    );
  });
}

export function deleteTransactionDocumentsRPC(
  authToken: string,
  transaction_id: string,
  fileNames: string[],
): Promise<{ ok: boolean }> {
  requestMetadata.set("authorization", authToken);

  return new Promise((resolve, reject) => {
    transactionClient.DeleteTransactionDocuments(
      {
        transaction_id,
        object_names: fileNames,
      },
      requestMetadata,
      (error) => {
        if (error) {
          reject(error);
        } else {
          resolve({
            ok: true,
          });
        }
      },
    );
  });
}

export function searchTransactionRPC(
  authToken: string,
  input: string,
  search_by: SearchBy,
  page_number: number,
  page_size: number,
  filters?: {
    timeRange?: { fromTime: number; toTime: number };
    amountRange?: { minAmount: number; maxAmount: number };
    transactionType?: string;
    tagStatus?: string;
    bookmarkOptions?: {
      showFavorites?: boolean;
      excludedFromCashflow?: boolean;
      withNotes?: boolean;
    };
  },
): Promise<{ cards: Transaction[] }> {
  requestMetadata.set("authorization", authToken);

  const request: any = {};

  if (filters) {
    request.filter = {
      txn_filters: {},
    };

    // Time range filter
    if (
      filters.timeRange &&
      (filters.timeRange.fromTime > 0 || filters.timeRange.toTime > 0)
    ) {
      request.filter.txn_filters.time_range = {
        from_time:
          filters.timeRange.fromTime > 0
            ? String(filters.timeRange.fromTime)
            : undefined,
        to_time:
          filters.timeRange.toTime > 0
            ? String(filters.timeRange.toTime)
            : undefined,
      };
      if (!request.filter.txn_filters.time_range.from_time) {
        delete request.filter.txn_filters.time_range.from_time;
      }
      if (!request.filter.txn_filters.time_range.to_time) {
        delete request.filter.txn_filters.time_range.to_time;
      }
      if (
        !request.filter.txn_filters.time_range.from_time &&
        !request.filter.txn_filters.time_range.to_time
      ) {
        delete request.filter.txn_filters.time_range;
      }
    }

    // Amount range filter
    if (
      filters.amountRange &&
      ((typeof filters.amountRange.minAmount === "number" &&
        filters.amountRange.minAmount > 0) ||
        (typeof filters.amountRange.maxAmount === "number" &&
          filters.amountRange.maxAmount > 0))
    ) {
      request.filter.txn_filters.amount_range = {};

      if (
        typeof filters.amountRange.minAmount === "number" &&
        filters.amountRange.minAmount > 0
      ) {
        request.filter.txn_filters.amount_range.min_amount = String(
          filters.amountRange.minAmount,
        );
      }

      if (
        typeof filters.amountRange.maxAmount === "number" &&
        filters.amountRange.maxAmount > 0
      ) {
        request.filter.txn_filters.amount_range.max_amount = String(
          filters.amountRange.maxAmount,
        );
      }
    }

    // Bookmark options filter
    if (filters.bookmarkOptions) {
      const { showFavorites, excludedFromCashflow, withNotes } =
        filters.bookmarkOptions;

      if (showFavorites) {
        request.filter.txn_filters.favorited = { favorited: true };
      }

      if (excludedFromCashflow) {
        request.filter.txn_filters.exclude_cash_flow = {
          exclude_cash_flow: true,
        };
      }

      if (withNotes) {
        request.filter.txn_filters.has_user_notes = { has_user_notes: true };
      }
    }
    // Transaction type
    if (filters.transactionType && filters.transactionType !== "all") {
      const txnType =
        filters.transactionType.toLowerCase() === "incoming"
          ? "CREDIT"
          : "DEBIT";
      request.filter.txn_filters.txn_type = { txn_type: txnType };
    }

    // Tag status
    if (filters.tagStatus && filters.tagStatus.toLowerCase() !== "all") {
      const isUntagged = filters.tagStatus.toLowerCase() === "untagged";
      request.filter.txn_filters.untagged = { untagged: isUntagged };
    }
  }
  request.input = input;
  request.search_by = search_by;
  request.pagination_params = {
    page_size: page_size,
    page_number: page_number,
  };

  return new Promise((resolve, reject) => {
    transactionClient.SearchTxns(
      request,
      requestMetadata,
      (error, response: any) => {
        if (error) {
          reject(error);
        } else {
          resolve({
            cards: response.cards,
          });
        }
      },
    );
  });
};
