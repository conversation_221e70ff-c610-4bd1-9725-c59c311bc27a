import * as grpc from "@grpc/grpc-js";
import * as protoLoader from "@grpc/proto-loader";
import path from "path";
import {
  Account,
  Category,
  FetchDocuments,
  Subcategory,
  TransactionTypeEnum,
} from "@repo/ui";

export enum SearchBy {
  SEARCH_BY_ALL = 0,
  SEARCH_BY_TAG = 1,
  SEARCH_BY_MERCHANT = 2,
  SEARCH_BY_USER = 3,
  SEARCH_BY_NOTES = 4,
  SEARCH_BY_AMOUNT = 5,
  SEARCH_BY_REMARKS = 6,
}

interface TransactionCategory {
  category_collection: string;
  category_id: string;
  subcategory_id: string;
}

interface TransactionMerchant {
  user_id: string;
  merchant_id: string;
  merchant_name: string;
}

export interface Transaction {
  txn_id: string;
  account_id: string;
  amount: number;
  type: TransactionTypeEnum;
  txn_timestamp: string;
  mode: string;
  narration: string;
  raw_txn_id: string;
  favorite: boolean;
  tag: TransactionCategory;
  exclude_cash_flow: boolean;
  merchant: TransactionMerchant;
  fip_id: string;
  user_notes: string;
  cash_flow_period: {
    month: number;
    year: number;
  };
  documents_count: number;

  account?: Account;
  category?: Category;
  subcategory?: Subcategory;
}

export interface GetTransactionResponse {
  cards: Transaction[];
}

type DepositTransactionClient = grpc.Client & {
  FetchDepositTxns: (
    request: object,
    metadata: grpc.Metadata,
    callback: (
      error: grpc.ServiceError | null,
      response: GetTransactionResponse,
    ) => void,
  ) => void;
  MarkDepositTxnFavorite: (
    request: {
      transaction_id: string;
      favorite: boolean;
    },
    metadata: grpc.Metadata,
    callback: (error: grpc.ServiceError | null, response: object) => void,
  ) => void;
  ExcludeTxnFromCashFlow: (
    request: {
      txn_id: string;
      exclude_cash_flow: boolean;
    },
    metadata: grpc.Metadata,
    callback: (error: grpc.ServiceError | null, response: object) => void,
  ) => void;
  UpdateTransactionNotes: (
    request: {
      transaction_id: string;
      notes: string;
    },
    metadata: grpc.Metadata,
    callback: (error: grpc.ServiceError | null, response: object) => void,
  ) => void;
  FetchTransactionDocuments: (
    request: {
      transaction_id: string;
    },
    metadata: grpc.Metadata,
    callback: (
      error: grpc.ServiceError | null,
      response: FetchDocuments,
    ) => void,
  ) => void;
  DeleteTransactionDocuments: (
    request: {
      transaction_id: string;
      object_names: string[];
    },
    metadata: grpc.Metadata,
    callback: (error: grpc.ServiceError | null, response: object) => void,
  ) => void;
  AssignCategoryToDepositTxns: (
    request: {
      transaction_ids: string[];
      category_id: string;
      collection: string;
      subcategory_id: string;
    },
    metadata: grpc.Metadata,
    callback: (error: grpc.ServiceError | null, response: object) => void,
  ) => void;
  SearchTxns: (
    request: {
      input: string;
      search_by: SearchBy;
    },
    metadata: grpc.Metadata,
    callback: (error: grpc.ServiceError | null, response: object) => void,
  ) => void;
};

const PROTO_DIR_PATH: string = process.env.PROTO_DIR_PATH || "";
const PROTO_FILE: string =
  "backend_services/visualization/deposit_transaction.proto";

const TRANSACTION_PROTO_FILE_PATH: string = path.resolve(
  PROTO_DIR_PATH,
  PROTO_FILE,
);

const options: protoLoader.Options = {
  keepCase: true,
  longs: String,
  enums: String,
  defaults: true,
  oneofs: true,
  includeDirs: [path.resolve(PROTO_DIR_PATH)],
};

let cachedTransactionClient: DepositTransactionClient | null = null;

const packageDefinition = protoLoader.loadSync(
  TRANSACTION_PROTO_FILE_PATH,
  options,
);
const txnProto = grpc.loadPackageDefinition(packageDefinition) as any;

const transactionService = txnProto.backend_services.visualization;

const requestMetadata = new grpc.Metadata();

export function createTransactionServiceClient() {
  if (cachedTransactionClient) {
    return cachedTransactionClient;
  }
  const target: string = process.env.GRPC_SERVER_URI || "";
  const client = new transactionService.DepositTransaction(
    target,
    grpc.credentials.createSsl(),
  ) as DepositTransactionClient;
  cachedTransactionClient = client;
  return client;
}

interface userId {
  id: string;
}
interface user_groups {
  user_ids: userId[];
  user_group_ids: userId[];
}

const transactionClient: DepositTransactionClient =
  createTransactionServiceClient();

export function getTransactions(
  authToken: string,
  pageSize: number,
  pageNumber: number,
  filters?: {
    user_groups?: user_groups;
    account_filters?: { account_ids?: string[] };
    timeRange?: { fromTime: number; toTime: number };
    amountRange?: { minAmount: number; maxAmount: number };
    transactionType?: string;
    tagStatus?: string;
    bookmarkOptions?: {
      showFavorites?: boolean;
      excludedFromCashflow?: boolean;
      withNotes?: boolean;
    };
    categories?: Array<{
      category_collection: string;
      category_id: string;
      subcategory_id: string;
    }>;
  },
): Promise<GetTransactionResponse> {
  requestMetadata.set("authorization", authToken);
  const request: any = {
    filter: {},
  };

  if (filters) {
    if (
      filters.user_groups?.user_ids?.length ||
      filters.user_groups?.user_group_ids?.length
    ) {
      request.filter.user_groups = {};

      if (filters.user_groups?.user_ids?.length) {
        request.filter.user_groups.user_ids = filters.user_groups.user_ids;
      }
      if (filters.user_groups?.user_group_ids?.length) {
        request.filter.user_groups.user_group_ids =
          filters.user_groups.user_group_ids;
      }
    }

    if (filters.account_filters?.account_ids?.length) {
      request.filter.account_filters = {
        account_ids: filters.account_filters.account_ids.map((id: any) => id),
      };
    }

    request.filter.txn_filters = {};

    // Time range filter
    if (
      filters.timeRange &&
      (filters.timeRange.fromTime > 0 || filters.timeRange.toTime > 0)
    ) {
      request.filter.txn_filters.time_range = {
        from_time:
          filters.timeRange.fromTime > 0
            ? String(filters.timeRange.fromTime)
            : undefined,
        to_time:
          filters.timeRange.toTime > 0
            ? String(filters.timeRange.toTime)
            : undefined,
      };
      if (!request.filter.txn_filters.time_range.from_time) {
        delete request.filter.txn_filters.time_range.from_time;
      }
      if (!request.filter.txn_filters.time_range.to_time) {
        delete request.filter.txn_filters.time_range.to_time;
      }
      if (
        !request.filter.txn_filters.time_range.from_time &&
        !request.filter.txn_filters.time_range.to_time
      ) {
        delete request.filter.txn_filters.time_range;
      }
    }

    // Amount range filter
    if (
      filters.amountRange &&
      ((typeof filters.amountRange.minAmount === "number" &&
        filters.amountRange.minAmount > 0) ||
        (typeof filters.amountRange.maxAmount === "number" &&
          filters.amountRange.maxAmount > 0))
    ) {
      request.filter.txn_filters.amount_range = {};

      if (
        typeof filters.amountRange.minAmount === "number" &&
        filters.amountRange.minAmount > 0
      ) {
        request.filter.txn_filters.amount_range.min_amount = String(
          filters.amountRange.minAmount,
        );
      }

      if (
        typeof filters.amountRange.maxAmount === "number" &&
        filters.amountRange.maxAmount > 0
      ) {
        request.filter.txn_filters.amount_range.max_amount = String(
          filters.amountRange.maxAmount,
        );
      }
    }

    // Bookmark options filter
    if (filters.bookmarkOptions) {
      const { showFavorites, excludedFromCashflow, withNotes } =
        filters.bookmarkOptions;

      if (showFavorites) {
        request.filter.txn_filters.favorited = { favorited: true };
      }

      if (excludedFromCashflow) {
        request.filter.txn_filters.exclude_cash_flow = {
          exclude_cash_flow: true,
        };
      }

      if (withNotes) {
        request.filter.txn_filters.has_user_notes = { has_user_notes: true };
      }
    }

    // Transaction type
    if (filters.transactionType && filters.transactionType !== "all") {
      const txnType =
        filters.transactionType.toLowerCase() === "incoming"
          ? "CREDIT"
          : "DEBIT";
      request.filter.txn_filters.txn_type = { txn_type: txnType };
    }

    // Tag status
    if (filters.tagStatus && filters.tagStatus.toLowerCase() !== "all") {
      const isUntagged = filters.tagStatus.toLowerCase() === "untagged";
      request.filter.txn_filters.untagged = { untagged: isUntagged };
    }

    // Categories filter
    if (filters.categories?.length) {
      request.filter.txn_filters.category = filters.categories.map(
        (category) => ({
          category_collection: category.category_collection || "global",
          category_id: category.category_id,
          subcategory_id: category.subcategory_id,
        }),
      );
    }
  }

  request.pagination_params = {
    page_size: pageSize,
    page_number: pageNumber,
  };

  return new Promise((resolve, reject) => {
    transactionClient.FetchDepositTxns(
      request,
      requestMetadata,
      (error, response) => {
        if (error) {
          console.error("FetchDepositTxns error:", error);
          reject(error);
        } else {
          resolve(response);
        }
      },
    );
  });
}

export function saveTransaction(
  authToken: string,
  transaction_id: string,
  favorite: boolean,
): Promise<object> {
  requestMetadata.set("authorization", authToken);
  return new Promise((resolve, reject) => {
    transactionClient.MarkDepositTxnFavorite(
      {
        transaction_id,
        favorite,
      },
      requestMetadata,
      (error, response) => {
        if (error) {
          reject(error);
        } else {
          resolve(response);
        }
      },
    );
  });
}

export function assignCategoryToTransaction(
  authToken: string,
  transaction_id: string,
  category_id: string,
  subcategory_id: string,
  collection: string = "",
): Promise<object> {
  requestMetadata.set("authorization", authToken);

  return new Promise((resolve, reject) => {
    transactionClient.AssignCategoryToDepositTxns(
      {
        transaction_ids: [transaction_id],
        category_id,
        subcategory_id,
        collection,
      },
      requestMetadata,
      (error, response) => {
        if (error) {
          reject(error);
        } else {
          resolve(response);
        }
      },
    );
  });
}

export function excludeTransactionFromCashFlow(
  authToken: string,
  transaction_id: string,
  flag: boolean,
): Promise<object> {
  requestMetadata.set("authorization", authToken);

  return new Promise((resolve, reject) => {
    transactionClient.ExcludeTxnFromCashFlow(
      {
        txn_id: transaction_id,
        exclude_cash_flow: flag,
      },
      requestMetadata,
      (error, response) => {
        if (error) {
          reject(error);
        } else {
          resolve(response);
        }
      },
    );
  });
}

export function updateTransactionNotes(
  authToken: string,
  transaction_id: string,
  notes: string,
): Promise<object> {
  requestMetadata.set("authorization", authToken);

  return new Promise((resolve, reject) => {
    transactionClient.UpdateTransactionNotes(
      {
        transaction_id,
        notes,
      },
      requestMetadata,
      (error, response) => {
        if (error) {
          reject(error);
        } else {
          resolve(response);
        }
      },
    );
  });
}

export function fetchTransactionDocumentsRPC(
  authToken: string,
  transaction_id: string,
): Promise<FetchDocuments> {
  requestMetadata.set("authorization", authToken);

  return new Promise((resolve, reject) => {
    transactionClient.FetchTransactionDocuments(
      {
        transaction_id,
      },
      requestMetadata,
      (error, response) => {
        if (error) {
          reject(error);
        } else {
          resolve(response);
        }
      },
    );
  });
}

export function deleteTransactionDocumentsRPC(
  authToken: string,
  transaction_id: string,
  fileNames: string[],
): Promise<{ ok: boolean }> {
  requestMetadata.set("authorization", authToken);

  return new Promise((resolve, reject) => {
    transactionClient.DeleteTransactionDocuments(
      {
        transaction_id,
        object_names: fileNames,
      },
      requestMetadata,
      (error, response) => {
        if (error) {
          reject(error);
        } else {
          resolve({
            ok: true,
          });
        }
      },
    );
  });
}

export function searchTransactionRPC(
  authToken: string,
  input: string,
  search_by: SearchBy,
  page_number: number,
  page_size: number,
  filters?: {
    timeRange?: { fromTime: number; toTime: number };
    amountRange?: { minAmount: number; maxAmount: number };
    transactionType?: string;
    tagStatus?: string;
    bookmarkOptions?: {
      showFavorites?: boolean;
      excludedFromCashflow?: boolean;
      withNotes?: boolean;
    };
  },
): Promise<{ cards: Transaction[] }> {
  requestMetadata.set("authorization", authToken);

  const request: any = {};

  if (filters) {
    request.filter = {
      txn_filters: {},
    };

    // Time range filter
    if (
      filters.timeRange &&
      (filters.timeRange.fromTime > 0 || filters.timeRange.toTime > 0)
    ) {
      request.filter.txn_filters.time_range = {
        from_time:
          filters.timeRange.fromTime > 0
            ? String(filters.timeRange.fromTime)
            : undefined,
        to_time:
          filters.timeRange.toTime > 0
            ? String(filters.timeRange.toTime)
            : undefined,
      };
      if (!request.filter.txn_filters.time_range.from_time) {
        delete request.filter.txn_filters.time_range.from_time;
      }
      if (!request.filter.txn_filters.time_range.to_time) {
        delete request.filter.txn_filters.time_range.to_time;
      }
      if (
        !request.filter.txn_filters.time_range.from_time &&
        !request.filter.txn_filters.time_range.to_time
      ) {
        delete request.filter.txn_filters.time_range;
      }
    }

    // Amount range filter
    if (
      filters.amountRange &&
      ((typeof filters.amountRange.minAmount === "number" &&
        filters.amountRange.minAmount > 0) ||
        (typeof filters.amountRange.maxAmount === "number" &&
          filters.amountRange.maxAmount > 0))
    ) {
      request.filter.txn_filters.amount_range = {};

      if (
        typeof filters.amountRange.minAmount === "number" &&
        filters.amountRange.minAmount > 0
      ) {
        request.filter.txn_filters.amount_range.min_amount = String(
          filters.amountRange.minAmount,
        );
      }

      if (
        typeof filters.amountRange.maxAmount === "number" &&
        filters.amountRange.maxAmount > 0
      ) {
        request.filter.txn_filters.amount_range.max_amount = String(
          filters.amountRange.maxAmount,
        );
      }
    }

    // Bookmark options filter
    if (filters.bookmarkOptions) {
      const { showFavorites, excludedFromCashflow, withNotes } =
        filters.bookmarkOptions;

      if (showFavorites) {
        request.filter.txn_filters.favorited = { favorited: true };
      }

      if (excludedFromCashflow) {
        request.filter.txn_filters.exclude_cash_flow = {
          exclude_cash_flow: true,
        };
      }

      if (withNotes) {
        request.filter.txn_filters.has_user_notes = { has_user_notes: true };
      }
    }
    // Transaction type
    if (filters.transactionType && filters.transactionType !== "all") {
      const txnType =
        filters.transactionType.toLowerCase() === "incoming"
          ? "CREDIT"
          : "DEBIT";
      request.filter.txn_filters.txn_type = { txn_type: txnType };
    }

    // Tag status
    if (filters.tagStatus && filters.tagStatus.toLowerCase() !== "all") {
      const isUntagged = filters.tagStatus.toLowerCase() === "untagged";
      request.filter.txn_filters.untagged = { untagged: isUntagged };
    }
  }
  request.input = input;
  request.search_by = search_by;
  request.pagination_params = {
    page_size: page_size,
    page_number: page_number,
  };

  return new Promise((resolve, reject) => {
    transactionClient.SearchTxns(
      request,
      requestMetadata,
      (error, response: any) => {
        if (error) {
          reject(error);
        } else {
          resolve({
            cards: response.cards,
          });
        }
      },
    );
  });
};
