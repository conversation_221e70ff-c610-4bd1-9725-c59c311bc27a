import { TransactionFilters, TransactionRequest } from "../types/transaction.types";

export function applyUserGroupFilters(filters: TransactionFilters, request: TransactionRequest) {
  if ('userGroups' in filters && filters.userGroups) {
    request.filter.user_groups = {};

    if (filters.userGroups.userIds?.length) {
      request.filter.user_groups.user_ids = filters.userGroups.userIds;
    }

    if (filters.userGroups.userGroupIds?.length) {
      request.filter.user_groups.user_group_ids = filters.userGroups.userGroupIds;
    }
  }
}

export function applyAccountFilters(filters: TransactionFilters, request: TransactionRequest) {
  if ('accountFilters' in filters && filters.accountFilters?.accountIds?.length) {
    request.filter.account_filters = {
      account_ids: filters.accountFilters.accountIds.map((id: string) => id),
    };
  }
}

export function applyTimeRangeFilter(filters: TransactionFilters, request: TransactionRequest) {
  if (
    filters.timeRange &&
    (filters.timeRange.fromTime > 0 || filters.timeRange.toTime > 0)
  ) {
    request.filter.txn_filters = request.filter.txn_filters || {};
    request.filter.txn_filters.time_range = {
      from_time:
        filters.timeRange.fromTime > 0
          ? String(filters.timeRange.fromTime)
          : undefined,
      to_time:
        filters.timeRange.toTime > 0
          ? String(filters.timeRange.toTime)
          : undefined,
    };

    if (!request.filter.txn_filters.time_range.from_time) {
      delete request.filter.txn_filters.time_range.from_time;
    }
    if (!request.filter.txn_filters.time_range.to_time) {
      delete request.filter.txn_filters.time_range.to_time;
    }
    if (
      !request.filter.txn_filters.time_range.from_time &&
      !request.filter.txn_filters.time_range.to_time
    ) {
      delete request.filter.txn_filters.time_range;
    }
  }
}

export function applyAmountRangeFilter(filters: TransactionFilters, request: TransactionRequest) {
  if (
    filters.amountRange &&
    ((typeof filters.amountRange.minAmount === "number" &&
      filters.amountRange.minAmount > 0) ||
      (typeof filters.amountRange.maxAmount === "number" &&
        filters.amountRange.maxAmount > 0))
  ) {
    request.filter.txn_filters = request.filter.txn_filters || {};
    request.filter.txn_filters.amount_range = {};

    if (
      typeof filters.amountRange.minAmount === "number" &&
      filters.amountRange.minAmount > 0
    ) {
      request.filter.txn_filters.amount_range.min_amount = String(
        filters.amountRange.minAmount,
      );
    }

    if (
      typeof filters.amountRange.maxAmount === "number" &&
      filters.amountRange.maxAmount > 0
    ) {
      request.filter.txn_filters.amount_range.max_amount = String(
        filters.amountRange.maxAmount,
      );
    }
  }
}

export function applyBookmarkFilters(filters: TransactionFilters, request: TransactionRequest) {
  if (filters.bookmarkOptions) {
    request.filter.txn_filters = request.filter.txn_filters || {};
    const { showFavorites, excludedFromCashflow, withNotes } = filters.bookmarkOptions;

    if (showFavorites) {
      request.filter.txn_filters.favorited = { favorited: true };
    }

    if (excludedFromCashflow) {
      request.filter.txn_filters.exclude_cash_flow = {
        exclude_cash_flow: true,
      };
    }

    if (withNotes) {
      request.filter.txn_filters.has_user_notes = { has_user_notes: true };
    }
  }
}

export function applyTransactionTypeFilter(filters: TransactionFilters, request: TransactionRequest) {
  if (filters.transactionType && filters.transactionType !== "all") {
    request.filter.txn_filters = request.filter.txn_filters || {};
    const txnType = filters.transactionType.toLowerCase() === "incoming" ? "CREDIT" : "DEBIT";
    request.filter.txn_filters.txn_type = { txn_type: txnType };
  }
}

export function applyTagStatusFilter(filters: TransactionFilters, request: TransactionRequest) {
  if (filters.tagStatus && filters.tagStatus.toLowerCase() !== "all") {
    request.filter.txn_filters = request.filter.txn_filters || {};
    const isUntagged = filters.tagStatus.toLowerCase() === "untagged";
    request.filter.txn_filters.untagged = { untagged: isUntagged };
  }
}

export function applyCategoriesFilter(filters: TransactionFilters, request: TransactionRequest) {
  if (filters.categories?.length) {
    request.filter.txn_filters = request.filter.txn_filters || {};
    request.filter.txn_filters.category = filters.categories.map((category) => {
      if ('categoryId' in category) {
        return {
          category_collection: category.categoryCollection || "global",
          category_id: category.categoryId,
          subcategory_id: category.subcategoryId || "",
        };
      }
      return {
        category_collection: "global",
        category_id: "",
        subcategory_id: "",
      };
    });
  }
}

export function applyTransactionFilters(filters: TransactionFilters): TransactionRequest {
  const request: TransactionRequest = {
    filter: {},
    pagination_params: {
      page_size: 0,  // These will be set by the caller
      page_number: 0,
    },
  };

  if (filters) {
    applyUserGroupFilters(filters, request);
    applyAccountFilters(filters, request);
    request.filter.txn_filters = {};
    applyTimeRangeFilter(filters, request);
    applyAmountRangeFilter(filters, request);
    applyBookmarkFilters(filters, request);
    applyTransactionTypeFilter(filters, request);
    applyTagStatusFilter(filters, request);
    applyCategoriesFilter(filters, request);
  }

  return request;
}