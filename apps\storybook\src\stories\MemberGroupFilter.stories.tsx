import type { <PERSON><PERSON>, StoryObj } from "@storybook/react";
import { dummyAccount, MemberGroupFilter, SelectedAccountsType } from "@repo/ui";
import { dummyGroups, dummyMembers } from "@repo/ui";
import { useState } from "react";

const meta: Meta<typeof MemberGroupFilter> = {
  title: "Filters/MemberGroupFilter",
  component: MemberGroupFilter,
  tags: ["autodocs"],
  decorators: [
    (Story) => (
      <div style={{ width: "400px" }}>
        <Story />
      </div>
    ),
  ],
};

export default meta;
type Story = StoryObj<typeof MemberGroupFilter>;

const userAccountsMap = dummyAccount.accounts.reduce((acc, account) => {
  if (!acc[account.userId]) {
    acc[account.userId] = [];
  }
  acc?.[account?.userId!]!.push(account);
  return acc;
}, {} as Record<string, typeof dummyAccount.accounts>);

const mappedDummyMembers = dummyMembers.users.map((user) => ({
  id: user._id.id,
  name: user.name || user.nickname || user.phoneNumber,
  accounts: userAccountsMap[user._id.id] || [],
}));
const mappedDummyGroups = dummyGroups.groups.map((groupData) => {
  const mappedMembers = groupData.users.map((user) => ({
    id: user._id.id,
    name: user.name || user.nickname || user.phoneNumber,
    accounts: userAccountsMap[user._id.id] || [],
  }));

  const membersWithAccounts = mappedMembers.filter(member => member.accounts.length > 0);

  return {
    id: groupData.group._id.id,
    groupName: groupData.group.name,
    members: membersWithAccounts,
  };
});

const MemberGroupFilters = () => {
  const [selectedMembers, setSelectedMembers] = useState<string[]>([]);
  const [selectedGroups, setSelectedGroups] = useState<string[]>([]);
  const [selectedAccounts, setSelectedAccounts] = useState<SelectedAccountsType>({
    accountIds: new Set<string>()
  });

  const handleSelectionChange = (
    memberData: any[],
    groupData: any[],
    accounts: SelectedAccountsType
  ) => {
    setSelectedMembers(memberData.map(m => m.memberId));
    setSelectedGroups(groupData.map(g => g.groupId));
    setSelectedAccounts(accounts);
  };

  return (
    <MemberGroupFilter
      members={mappedDummyMembers}
      groups={mappedDummyGroups}
      selectedMembers={selectedMembers}
      selectedGroups={selectedGroups}
      selectedAccounts={selectedAccounts}
      onSelectionChange={handleSelectionChange}
    />
  );
};

export const Default: Story = {
  render: () => <MemberGroupFilters />
};